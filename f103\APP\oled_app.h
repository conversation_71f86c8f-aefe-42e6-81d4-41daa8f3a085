#ifndef __OLED_APP_H__
#define __OLED_APP_H__

#include "mydefine.h"

// 小人跳舞动画相关数据结构
typedef struct
{
    uint8_t x, y, radius; // 头部圆心坐标和半径
} DancingManHead;

typedef struct
{
    uint8_t x1, y1, x2, y2; // 线条起点和终点坐标
} DancingManLine;

typedef struct
{
    DancingManHead head;      // 头部
    DancingManLine body;      // 身体
    DancingManLine left_arm;  // 左臂
    DancingManLine right_arm; // 右臂
    DancingManLine left_leg;  // 左腿
    DancingManLine right_leg; // 右腿
    int8_t y_offset;          // 整体Y轴偏移(用于跳跃效果)
} DancingManFrame;

void oled_app_init(void); // OLED 应用初始化函数
void oled_test_animation_task(void);
void oled_metaballs_task(void);
void oled_dancing_man_task(void); // 小人跳舞动画任务

// 动画控制函数
void dancing_man_pause(void);                  // 暂停动画
void dancing_man_resume(void);                 // 恢复动画
void dancing_man_reset(void);                  // 重置动画到第一帧
void dancing_man_set_speed(uint32_t speed_ms); // 设置动画速度(每帧间隔ms)
#endif
