/**
 ******************************************************************************
 * @file    ssd1306.c
 * <AUTHOR> by Aleksander Alekseev, modified by others)
 * @version V2.1.0
 * @date    10-February-2021
 * @brief   This file provides a set of functions needed to manage the SSD1306
 *          OLED display.
 * @note    This version is modified to:
 *          1. Use HAL I2C with DMA for efficient data transfer.
 *          2. Optimize the screen update function for horizontal addressing mode.
 *          3. Make the screen buffer global for external access.
 ******************************************************************************
 */

#include "ssd1306.h"
#include "ssd1306_fonts.h"
#include <math.h>
#include <stdlib.h>
#include <string.h>  // For memcpy

/*================================================================================================*/
/* I2C / SPI Communication Abstraction                                                            */
/*================================================================================================*/

#if defined(SSD1306_USE_I2C)

/**
 * @brief  Resets the OLED. Not used for I2C.
 */
void ssd1306_Reset(void) {
    /* For I2C, reset is not typically handled via a GPIO pin */
}

/**
 * @brief  Sends a single command byte to the OLED.
 * @param  byte: The command byte to send.
 * @note   Uses blocking (polling) mode as single-byte transfers are very fast.
 */
void ssd1306_WriteCommand(uint8_t byte) {
    HAL_I2C_Mem_Write(&SSD1306_I2C_PORT, SSD1306_I2C_ADDR, 0x00, 1, &byte, 1, HAL_MAX_DELAY);
}

/**
 * @brief  Sends a buffer of data to the OLED.
 * @param  buffer: Pointer to the data buffer.
 * @param  buff_size: The size of the buffer.
 * @note   This function uses DMA for efficient transfer, freeing up the CPU.
 */
void ssd1306_WriteData(uint8_t* buffer, size_t buff_size) {
    // Start the I2C transfer in DMA mode. This function returns immediately.
    HAL_StatusTypeDef status = HAL_I2C_Mem_Write_DMA(&SSD1306_I2C_PORT, SSD1306_I2C_ADDR, 0x40, 1, buffer, buff_size);

    // Only wait if the DMA transfer was successfully initiated.
    if (status == HAL_OK) {
        // Wait until the I2C peripheral is ready again.
        // The HAL I2C DMA handler will set the state to READY upon completion (via interrupt).
        while (HAL_I2C_GetState(&SSD1306_I2C_PORT) != HAL_I2C_STATE_READY) {
            // CPU is idle here. In an RTOS environment, a call to osDelay(1) would be better
            // to yield the CPU to other tasks.
        }
    }
    // Optional: Add error handling here if 'status' is not HAL_OK.
}

#elif defined(SSD1306_USE_SPI)

void ssd1306_Reset(void) {
    HAL_GPIO_WritePin(SSD1306_CS_Port, SSD1306_CS_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(SSD1306_Reset_Port, SSD1306_Reset_Pin, GPIO_PIN_RESET);
    HAL_Delay(10);
    HAL_GPIO_WritePin(SSD1306_Reset_Port, SSD1306_Reset_Pin, GPIO_PIN_SET);
    HAL_Delay(10);
}

void ssd1306_WriteCommand(uint8_t byte) {
    HAL_GPIO_WritePin(SSD1306_CS_Port, SSD1306_CS_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(SSD1306_DC_Port, SSD1306_DC_Pin, GPIO_PIN_RESET);
    HAL_SPI_Transmit(&SSD1306_SPI_PORT, &byte, 1, HAL_MAX_DELAY);
    HAL_GPIO_WritePin(SSD1306_CS_Port, SSD1306_CS_Pin, GPIO_PIN_SET);
}

void ssd1306_WriteData(uint8_t* buffer, size_t buff_size) {
    HAL_GPIO_WritePin(SSD1306_CS_Port, SSD1306_CS_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(SSD1306_DC_Port, SSD1306_DC_Pin, GPIO_PIN_SET);
    HAL_SPI_Transmit(&SSD1306_SPI_PORT, buffer, buff_size, HAL_MAX_DELAY);
    HAL_GPIO_WritePin(SSD1306_CS_Port, SSD1306_CS_Pin, GPIO_PIN_SET);
}

#else
#error "You should define SSD1306_USE_SPI or SSD1306_USE_I2C macro in ssd1306_conf.h"
#endif

/*================================================================================================*/
/* Global Variables                                                                               */
/*================================================================================================*/

/**
 * @brief Screen buffer.
 * @note  The 'static' keyword has been removed to make this a global variable,
 *        allowing it to be accessed from other files (like oled_app.c)
 *        using the 'extern' keyword.
 */
uint8_t SSD1306_Buffer[SSD1306_BUFFER_SIZE];

// Screen object structure
static SSD1306_t SSD1306;


/*================================================================================================*/
/* Initialization and Core Functions                                                              */
/*================================================================================================*/

/**
 * @brief  Initializes the SSD1306 OLED display.
 */
void ssd1306_Init(void) {
    ssd1306_Reset();
    HAL_Delay(100);

    ssd1306_SetDisplayOn(0); // Display OFF

    ssd1306_WriteCommand(0x20); // Set Memory Addressing Mode
    ssd1306_WriteCommand(0x00); // 00b=Horizontal Addressing Mode

    ssd1306_WriteCommand(0xB0); // Set Page Start Address for Page Addressing Mode (0-7)

#ifdef SSD1306_MIRROR_VERT
    ssd1306_WriteCommand(0xC0); // Mirror vertically
#else
    ssd1306_WriteCommand(0xC8); // Set COM Output Scan Direction
#endif

    ssd1306_WriteCommand(0x00); // Set low column address
    ssd1306_WriteCommand(0x10); // Set high column address

    ssd1306_WriteCommand(0x40); // Set start line address

    ssd1306_SetContrast(0xFF); // Set contrast

#ifdef SSD1306_MIRROR_HORIZ
    ssd1306_WriteCommand(0xA0); // Mirror horizontally
#else
    ssd1306_WriteCommand(0xA1); // Set segment re-map 0 to 127
#endif

#ifdef SSD1306_INVERSE_COLOR
    ssd1306_WriteCommand(0xA7); // Set inverse color
#else
    ssd1306_WriteCommand(0xA6); // Set normal color
#endif

    ssd1306_WriteCommand(0xA8); // Set multiplex ratio
    ssd1306_WriteCommand(0x3F); // 1/64 duty

    ssd1306_WriteCommand(0xA4); // Output follows RAM content

    ssd1306_WriteCommand(0xD3); // Set display offset
    ssd1306_WriteCommand(0x00); // No offset

    ssd1306_WriteCommand(0xD5); // Set display clock divide ratio/oscillator frequency
    ssd1306_WriteCommand(0x80); // Set divide ratio

    ssd1306_WriteCommand(0xD9); // Set pre-charge period
    ssd1306_WriteCommand(0xF1);

    ssd1306_WriteCommand(0xDA); // Set com pins hardware configuration
    ssd1306_WriteCommand(0x12);

    ssd1306_WriteCommand(0xDB); // Set vcomh
    ssd1306_WriteCommand(0x40);

    ssd1306_WriteCommand(0x8D); // Set DC-DC enable
    ssd1306_WriteCommand(0x14);

    ssd1306_SetDisplayOn(1); // Display ON

    ssd1306_Fill(Black);
    ssd1306_UpdateScreen();

    SSD1306.CurrentX = 0;
    SSD1306.CurrentY = 0;
    SSD1306.Initialized = 1;
}

/**
 * @brief  Fills the entire screen buffer with a specified color.
 * @param  color: The color to fill with (Black or White).
 */
void ssd1306_Fill(SSD1306_COLOR color) {
    memset(SSD1306_Buffer, (color == Black) ? 0x00 : 0xFF, sizeof(SSD1306_Buffer));
}

/**
 * @brief  Writes the screen buffer to the OLED screen.
 * @note   This optimized version uses horizontal addressing mode to update the
 *         entire screen in a single, large data transfer, which is ideal for DMA.
 */
void ssd1306_UpdateScreen(void) {
    // Set the column address range from 0 to 127
    ssd1306_WriteCommand(0x21); // Command: Set Column Address
    ssd1306_WriteCommand(0);    // Column Start Address
    ssd1306_WriteCommand(SSD1306_WIDTH - 1); // Column End Address

    // Set the page address range from 0 to 7 (for 64-pixel high display)
    ssd1306_WriteCommand(0x22); // Command: Set Page Address
    ssd1306_WriteCommand(0);    // Page Start Address
    ssd1306_WriteCommand(SSD1306_HEIGHT / 8 - 1); // Page End Address

    // Transfer the entire screen buffer at once using DMA
    ssd1306_WriteData(SSD1306_Buffer, SSD1306_BUFFER_SIZE);
}

/**
 * @brief  Draws a single pixel in the screen buffer.
 * @param  x: The x-coordinate.
 * @param  y: The y-coordinate.
 * @param  color: The pixel color.
 */
void ssd1306_DrawPixel(uint8_t x, uint8_t y, SSD1306_COLOR color) {
    if (x >= SSD1306_WIDTH || y >= SSD1306_HEIGHT) {
        return;
    }

    if (color == White) {
        SSD1306_Buffer[x + (y / 8) * SSD1306_WIDTH] |= (1 << (y % 8));
    } else {
        SSD1306_Buffer[x + (y / 8) * SSD1306_WIDTH] &= ~(1 << (y % 8));
    }
}


/*================================================================================================*/
/* Drawing Functions (Text, Shapes, etc.)                                                         */
/*================================================================================================*/

/**
 * @brief  Writes a single character to the screen buffer.
 * @param  ch: The character to write.
 * @param  Font: The font to use.
 * @param  color: The color of the character.
 * @return The written character, or 0 if it could not be written.
 */
char ssd1306_WriteChar(char ch, SSD1306_Font_t Font, SSD1306_COLOR color) {
    uint32_t i, b, j;

    if (ch < 32 || ch > 126) return 0;

    if (SSD1306_WIDTH < (SSD1306.CurrentX + Font.width) ||
        SSD1306_HEIGHT < (SSD1306.CurrentY + Font.height)) {
        return 0;
    }

    for (i = 0; i < Font.height; i++) {
        b = Font.data[(ch - 32) * Font.height + i];
        for (j = 0; j < Font.width; j++) {
            if ((b << j) & 0x8000) {
                ssd1306_DrawPixel(SSD1306.CurrentX + j, (SSD1306.CurrentY + i), color);
            } else {
                ssd1306_DrawPixel(SSD1306.CurrentX + j, (SSD1306.CurrentY + i), (color == White) ? Black : White);
            }
        }
    }

    SSD1306.CurrentX += Font.width;
    return ch;
}

/**
 * @brief  Writes a string to the screen buffer.
 * @param  str: The string to write.
 * @param  Font: The font to use.
 * @param  color: The color of the string.
 * @return The last character written.
 */
char ssd1306_WriteString(char* str, SSD1306_Font_t Font, SSD1306_COLOR color) {
    while (*str) {
        if (ssd1306_WriteChar(*str, Font, color) != *str) {
            return *str;
        }
        str++;
    }
    return *str;
}

/**
 * @brief  Sets the cursor position for writing text.
 * @param  x: The x-coordinate.
 * @param  y: The y-coordinate.
 */
void ssd1306_SetCursor(uint8_t x, uint8_t y) {
    SSD1306.CurrentX = x;
    SSD1306.CurrentY = y;
}

/**
 * @brief  Draws a line using Bresenham's algorithm.
 */
void ssd1306_Line(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2, SSD1306_COLOR color) {
    int32_t deltaX = abs(x2 - x1);
    int32_t deltaY = abs(y2 - y1);
    int32_t signX = (x1 < x2) ? 1 : -1;
    int32_t signY = (y1 < y2) ? 1 : -1;
    int32_t error = deltaX - deltaY;
    int32_t error2;

    ssd1306_DrawPixel(x2, y2, color);
    while (x1 != x2 || y1 != y2) {
        ssd1306_DrawPixel(x1, y1, color);
        error2 = error * 2;
        if (error2 > -deltaY) {
            error -= deltaY;
            x1 += signX;
        }
        if (error2 < deltaX) {
            error += deltaX;
            y1 += signY;
        }
    }
}

/**
 * @brief  Draws a rectangle.
 */
void ssd1306_DrawRectangle(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2, SSD1306_COLOR color) {
    ssd1306_Line(x1, y1, x2, y1, color);
    ssd1306_Line(x2, y1, x2, y2, color);
    ssd1306_Line(x2, y2, x1, y2, color);
    ssd1306_Line(x1, y2, x1, y1, color);
}

/**
 * @brief  Draws a filled rectangle.
 */
void ssd1306_FillRectangle(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2, SSD1306_COLOR color) {
    uint8_t x_start = (x1 <= x2) ? x1 : x2;
    uint8_t x_end = (x1 <= x2) ? x2 : x1;
    uint8_t y_start = (y1 <= y2) ? y1 : y2;
    uint8_t y_end = (y1 <= y2) ? y2 : y1;

    for (uint8_t y = y_start; y <= y_end && y < SSD1306_HEIGHT; y++) {
        for (uint8_t x = x_start; x <= x_end && x < SSD1306_WIDTH; x++) {
            ssd1306_DrawPixel(x, y, color);
        }
    }
}

/**
 * @brief  Draws a circle using Bresenham's algorithm.
 */
void ssd1306_DrawCircle(uint8_t x, uint8_t y, uint8_t r, SSD1306_COLOR color) {
    int32_t d;
    int32_t curX;
    int32_t curY;

    d = 3 - (r << 1);
    curX = 0;
    curY = r;

    while (curX <= curY) {
        ssd1306_DrawPixel(x + curX, y + curY, color);
        ssd1306_DrawPixel(x - curX, y + curY, color);
        ssd1306_DrawPixel(x + curX, y - curY, color);
        ssd1306_DrawPixel(x - curX, y - curY, color);
        ssd1306_DrawPixel(x + curY, y + curX, color);
        ssd1306_DrawPixel(x - curY, y + curX, color);
        ssd1306_DrawPixel(x + curY, y - curX, color);
        ssd1306_DrawPixel(x - curY, y - curX, color);

        if (d < 0) {
            d += (curX << 2) + 6;
        } else {
            d += ((curX - curY) << 2) + 10;
            curY--;
        }
        curX++;
    }
}

/**
 * @brief  Draws a filled circle.
 */
void ssd1306_FillCircle(uint8_t x, uint8_t y, uint8_t r, SSD1306_COLOR color) {
    int32_t d;
    int32_t curX;
    int32_t curY;

    d = 3 - (r << 1);
    curX = 0;
    curY = r;

    while (curX <= curY) {
        // Draw horizontal lines to fill the circle
        for (int i = x - curX; i <= x + curX; i++) {
            ssd1306_DrawPixel(i, y + curY, color);
            ssd1306_DrawPixel(i, y - curY, color);
        }
        for (int i = x - curY; i <= x + curY; i++) {
            ssd1306_DrawPixel(i, y + curX, color);
            ssd1306_DrawPixel(i, y - curX, color);
        }

        if (d < 0) {
            d += (curX << 2) + 6;
        } else {
            d += ((curX - curY) << 2) + 10;
            curY--;
        }
        curX++;
    }
}

/**
 * @brief  Sets the display contrast.
 * @param  value: The contrast value (0-255).
 */
void ssd1306_SetContrast(const uint8_t value) {
    ssd1306_WriteCommand(0x81); // Command to set contrast
    ssd1306_WriteCommand(value);
}

/**
 * @brief  Turns the display on or off.
 * @param  on: 1 to turn on, 0 to turn off.
 */
void ssd1306_SetDisplayOn(const uint8_t on) {
    if (on) {
        ssd1306_WriteCommand(0xAF); // Display ON
        SSD1306.DisplayOn = 1;
    } else {
        ssd1306_WriteCommand(0xAE); // Display OFF
        SSD1306.DisplayOn = 0;
    }
}

/**
 * @brief  Gets the current display on/off status.
 * @return 1 if on, 0 if off.
 */
uint8_t ssd1306_GetDisplayOn() {
    return SSD1306.DisplayOn;
}

// ... Any other functions like DrawBitmap, etc., can be added here ...
