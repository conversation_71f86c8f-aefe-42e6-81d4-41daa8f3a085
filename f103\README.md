# STM32F103 OLED小人跳舞动画项目

## 项目简介

这是一个基于STM32F103C8T6微控制器的OLED显示项目，实现了生动的小人跳舞动画效果。项目使用SSD1306 OLED显示屏（128x64分辨率），通过I2C接口通信，支持DMA传输以提高性能。

## 主要功能

### 🎭 小人跳舞动画
- **6帧流畅动画**：包含站立、抬腿、跳跃、挥手等多种跳舞姿态
- **循环播放**：动画自动循环，营造连续跳舞效果
- **实时控制**：支持暂停、恢复、重置和速度调节
- **状态显示**：实时显示当前帧数、播放状态和速度信息

### ⚙️ 动画控制功能
- `dancing_man_pause()` - 暂停动画
- `dancing_man_resume()` - 恢复动画播放
- `dancing_man_reset()` - 重置到第一帧
- `dancing_man_set_speed(ms)` - 设置帧间隔时间(50-1000ms)

### 🔧 技术特性
- **高效绘制**：基于SSD1306硬件加速绘图函数
- **DMA传输**：使用DMA提高屏幕刷新效率
- **任务调度**：集成到调度器系统，支持多任务运行
- **内存优化**：紧凑的数据结构，低内存占用

## 硬件要求

- **主控**：STM32F103C8T6 (Blue Pill)
- **显示屏**：SSD1306 OLED (128x64, I2C接口)
- **连接方式**：
  - VCC → 3.3V
  - GND → GND  
  - SCL → PB6 (I2C1_SCL)
  - SDA → PB7 (I2C1_SDA)

## 软件架构

### 文件结构
```
f103/
├── APP/
│   ├── oled_app.c          # OLED应用主文件，包含动画实现
│   ├── oled_app.h          # 动画数据结构和函数声明
│   ├── scheduler.c         # 任务调度器
│   └── mydefine.h          # 全局定义和配置
├── components/
│   └── oled/
│       ├── ssd1306.c       # SSD1306驱动库
│       ├── ssd1306.h       # 驱动头文件
│       └── ssd1306_fonts.c # 字体数据
└── Core/                   # STM32 HAL库文件
```

### 核心数据结构
```c
typedef struct {
    uint8_t x, y, radius;           // 头部圆心坐标和半径
} DancingManHead;

typedef struct {
    uint8_t x1, y1, x2, y2;        // 线条起点和终点坐标
} DancingManLine;

typedef struct {
    DancingManHead head;            // 头部
    DancingManLine body;            // 身体
    DancingManLine left_arm;        // 左臂
    DancingManLine right_arm;       // 右臂
    DancingManLine left_leg;        // 左腿
    DancingManLine right_leg;       // 右腿
    int8_t y_offset;               // 整体Y轴偏移(跳跃效果)
} DancingManFrame;
```

## 使用方法

### 基本使用
1. **编译项目**：使用Keil uVision5打开`f103.uvprojx`并编译
2. **烧录程序**：将生成的hex文件烧录到STM32F103C8T6
3. **连接硬件**：按照硬件要求连接OLED显示屏
4. **运行**：上电后自动开始播放小人跳舞动画

### 动画控制
```c
// 在代码中调用控制函数
dancing_man_pause();           // 暂停动画
dancing_man_resume();          // 恢复播放
dancing_man_reset();           // 重置到第一帧
dancing_man_set_speed(120);    // 设置120ms帧间隔
```

### 配置选项
- **帧率调节**：修改`frame_duration`变量(默认180ms)
- **显示信息**：设置`show_frame_info`控制是否显示调试信息
- **任务频率**：在`scheduler.c`中调整任务执行频率(默认20ms)

## 性能指标

- **内存使用**：
  - 代码空间：22.6KB
  - 只读数据：6.2KB
  - 可读写数据：60B
  - 零初始化数据：3.7KB
- **动画性能**：
  - 帧率：约5.5FPS (180ms/帧)
  - 刷新频率：50Hz (20ms任务周期)
  - 响应延迟：<20ms

## 技术实现细节

### 动画原理
1. **帧序列设计**：预定义6个关键帧，每帧包含小人各部位的坐标
2. **时间控制**：使用HAL_GetTick()获取系统时间，控制帧切换
3. **循环播放**：通过模运算实现帧索引的循环递增
4. **平滑过渡**：合理的帧间隔确保动画流畅自然

### 绘制优化
- **批量更新**：先绘制到缓冲区，再一次性更新到屏幕
- **DMA传输**：使用DMA减少CPU占用，提高传输效率
- **坐标计算**：预计算所有坐标，避免实时计算开销

## 扩展功能

### 可扩展方向
1. **更多动画**：添加不同的动画序列(走路、跑步等)
2. **交互控制**：通过按键或串口控制动画
3. **音效同步**：配合蜂鸣器播放音乐
4. **多角色**：同时显示多个小人动画

### 自定义动画
要添加新的动画帧，只需：
1. 在`dancing_frames[]`数组中添加新的帧数据
2. 更新`total_frames`常量
3. 根据需要调整`frame_duration`

## 故障排除

### 常见问题
1. **显示空白**：检查I2C连接和电源
2. **动画卡顿**：调整任务执行频率或帧间隔
3. **编译错误**：确保包含所有必要的头文件

### 调试方法
- 启用`show_frame_info`查看动画状态
- 使用串口输出调试信息
- 检查编译日志中的内存使用情况

## 开发环境

- **IDE**：Keil uVision5
- **编译器**：ARM Compiler 5.06
- **调试器**：ST-Link V2
- **目标芯片**：STM32F103C8T6

## 版本历史

- **v1.0** - 基础小人跳舞动画实现
- **v1.1** - 添加动画控制功能
- **v1.2** - 性能优化和状态显示

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目！

---
*项目作者：AI Assistant*  
*最后更新：2025年7月*
