Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) for DMA1_Channel5_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) for DMA1_Channel6_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.I2C1_ER_IRQHandler) for I2C1_ER_IRQHandler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to oled_app.o(i.oled_app_init) for oled_app_init
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to uart_app.o(i.my_printf) for my_printf
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to uart_app.o(.bss) for ringbuffer_pool
    main.o(i.main) refers to uart_app.o(.bss) for uart_ringbuffer
    main.o(i.main) refers to usart.o(.bss) for huart1
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    i2c.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to uart_app.o(.bss) for uart_rx_dma_buffer
    usart.o(i.fputc) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler) refers to i2c.o(.bss) for hdma_i2c1_tx
    stm32f1xx_it.o(i.I2C1_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) for HAL_I2C_ER_IRQHandler
    stm32f1xx_it.o(i.I2C1_ER_IRQHandler) refers to i2c.o(.bss) for hi2c1
    stm32f1xx_it.o(i.I2C1_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32f1xx_it.o(i.I2C1_EV_IRQHandler) refers to i2c.o(.bss) for hi2c1
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to uart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(.data) refers to oled_app.o(i.oled_dancing_man_task) for oled_dancing_man_task
    scheduler.o(.data) refers to uart_app.o(i.uart_task) for uart_task
    oled_app.o(i.dancing_man_pause) refers to oled_app.o(.data) for .data
    oled_app.o(i.dancing_man_reset) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled_app.o(i.dancing_man_reset) refers to oled_app.o(.data) for .data
    oled_app.o(i.dancing_man_resume) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled_app.o(i.dancing_man_resume) refers to oled_app.o(.data) for .data
    oled_app.o(i.dancing_man_set_speed) refers to oled_app.o(.data) for .data
    oled_app.o(i.dancing_man_test) refers to oled_app.o(i.dancing_man_reset) for dancing_man_reset
    oled_app.o(i.dancing_man_test) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled_app.o(i.dancing_man_test) refers to oled_app.o(i.dancing_man_resume) for dancing_man_resume
    oled_app.o(i.dancing_man_test) refers to oled_app.o(i.dancing_man_set_speed) for dancing_man_set_speed
    oled_app.o(i.dancing_man_test) refers to oled_app.o(.data) for .data
    oled_app.o(i.draw_dancing_man_frame) refers to ssd1306.o(i.ssd1306_DrawCircle) for ssd1306_DrawCircle
    oled_app.o(i.draw_dancing_man_frame) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    oled_app.o(i.metaballs_init) refers to rand.o(.emb_text) for rand
    oled_app.o(i.metaballs_init) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled_app.o(i.metaballs_init) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled_app.o(i.metaballs_init) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    oled_app.o(i.metaballs_init) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.oled_app_init) refers to ssd1306.o(i.ssd1306_Init) for ssd1306_Init
    oled_app.o(i.oled_app_init) refers to ssd1306.o(i.ssd1306_Fill) for ssd1306_Fill
    oled_app.o(i.oled_app_init) refers to ssd1306.o(i.ssd1306_SetCursor) for ssd1306_SetCursor
    oled_app.o(i.oled_app_init) refers to ssd1306.o(i.ssd1306_WriteString) for ssd1306_WriteString
    oled_app.o(i.oled_app_init) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    oled_app.o(i.oled_app_init) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    oled_app.o(i.oled_app_init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled_app.o(i.oled_app_init) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled_app.o(i.oled_app_init) refers to rand.o(.text) for srand
    oled_app.o(i.oled_app_init) refers to oled_app.o(i.metaballs_init) for metaballs_init
    oled_app.o(i.oled_app_init) refers to ssd1306_fonts.o(.constdata) for Font_11x18
    oled_app.o(i.oled_app_init) refers to ssd1306_fonts.o(.constdata) for Font_7x10
    oled_app.o(i.oled_dancing_man_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_app.o(i.oled_dancing_man_task) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    oled_app.o(i.oled_dancing_man_task) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    oled_app.o(i.oled_dancing_man_task) refers to _printf_dec.o(.text) for _printf_int_dec
    oled_app.o(i.oled_dancing_man_task) refers to _printf_str.o(.text) for _printf_str
    oled_app.o(i.oled_dancing_man_task) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled_app.o(i.oled_dancing_man_task) refers to ssd1306.o(i.ssd1306_Fill) for ssd1306_Fill
    oled_app.o(i.oled_dancing_man_task) refers to oled_app.o(i.draw_dancing_man_frame) for draw_dancing_man_frame
    oled_app.o(i.oled_dancing_man_task) refers to __2sprintf.o(.text) for __2sprintf
    oled_app.o(i.oled_dancing_man_task) refers to ssd1306.o(i.ssd1306_SetCursor) for ssd1306_SetCursor
    oled_app.o(i.oled_dancing_man_task) refers to ssd1306.o(i.ssd1306_WriteString) for ssd1306_WriteString
    oled_app.o(i.oled_dancing_man_task) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    oled_app.o(i.oled_dancing_man_task) refers to oled_app.o(.data) for .data
    oled_app.o(i.oled_dancing_man_task) refers to oled_app.o(.constdata) for .constdata
    oled_app.o(i.oled_dancing_man_task) refers to ssd1306_fonts.o(.constdata) for Font_6x8
    oled_app.o(i.oled_metaballs_task) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled_app.o(i.oled_metaballs_task) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    oled_app.o(i.oled_metaballs_task) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    oled_app.o(i.oled_metaballs_task) refers to ssd1306.o(i.ssd1306_Fill) for ssd1306_Fill
    oled_app.o(i.oled_metaballs_task) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled_app.o(i.oled_metaballs_task) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    oled_app.o(i.oled_metaballs_task) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    oled_app.o(i.oled_metaballs_task) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    oled_app.o(i.oled_metaballs_task) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    oled_app.o(i.oled_metaballs_task) refers to oled_app.o(.bss) for .bss
    oled_app.o(i.oled_test_animation_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled_app.o(i.oled_test_animation_task) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    oled_app.o(i.oled_test_animation_task) refers to _printf_dec.o(.text) for _printf_int_dec
    oled_app.o(i.oled_test_animation_task) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled_app.o(i.oled_test_animation_task) refers to rand.o(.emb_text) for rand
    oled_app.o(i.oled_test_animation_task) refers to __2sprintf.o(.text) for __2sprintf
    oled_app.o(i.oled_test_animation_task) refers to ssd1306.o(i.ssd1306_SetCursor) for ssd1306_SetCursor
    oled_app.o(i.oled_test_animation_task) refers to ssd1306.o(i.ssd1306_WriteString) for ssd1306_WriteString
    oled_app.o(i.oled_test_animation_task) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    oled_app.o(i.oled_test_animation_task) refers to oled_app.o(.data) for .data
    oled_app.o(i.oled_test_animation_task) refers to ssd1306.o(.bss) for SSD1306_Buffer
    oled_app.o(i.oled_test_animation_task) refers to ssd1306_fonts.o(.constdata) for Font_7x10
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal.o(.data) for uwTick
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to uart_app.o(.data) for .data
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    uart_app.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    uart_app.o(i.my_printf) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.uart_task) refers to uart_app.o(i.my_printf) for my_printf
    uart_app.o(i.uart_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_app.o(i.uart_task) refers to uart_app.o(.bss) for .bss
    uart_app.o(i.uart_task) refers to usart.o(.bss) for huart1
    ssd1306.o(i.ssd1306_DrawCircle) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_DrawPixel) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_DrawRectangle) refers to ssd1306.o(i.ssd1306_Line) for ssd1306_Line
    ssd1306.o(i.ssd1306_Fill) refers to aeabi_memset.o(.text) for __aeabi_memset
    ssd1306.o(i.ssd1306_Fill) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_FillCircle) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_FillRectangle) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_GetDisplayOn) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_SetDisplayOn) for ssd1306_SetDisplayOn
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_SetContrast) for ssd1306_SetContrast
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_Fill) for ssd1306_Fill
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(i.ssd1306_UpdateScreen) for ssd1306_UpdateScreen
    ssd1306.o(i.ssd1306_Init) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_Line) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_SetContrast) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_SetCursor) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_SetDisplayOn) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_SetDisplayOn) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_UpdateScreen) refers to ssd1306.o(i.ssd1306_WriteCommand) for ssd1306_WriteCommand
    ssd1306.o(i.ssd1306_UpdateScreen) refers to ssd1306.o(i.ssd1306_WriteData) for ssd1306_WriteData
    ssd1306.o(i.ssd1306_UpdateScreen) refers to ssd1306.o(.bss) for .bss
    ssd1306.o(i.ssd1306_WriteChar) refers to ssd1306.o(i.ssd1306_DrawPixel) for ssd1306_DrawPixel
    ssd1306.o(i.ssd1306_WriteChar) refers to ssd1306.o(.data) for .data
    ssd1306.o(i.ssd1306_WriteCommand) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    ssd1306.o(i.ssd1306_WriteCommand) refers to i2c.o(.bss) for hi2c1
    ssd1306.o(i.ssd1306_WriteData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) for HAL_I2C_Mem_Write_DMA
    ssd1306.o(i.ssd1306_WriteData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState) for HAL_I2C_GetState
    ssd1306.o(i.ssd1306_WriteData) refers to i2c.o(.bss) for hi2c1
    ssd1306.o(i.ssd1306_WriteString) refers to ssd1306.o(i.ssd1306_WriteChar) for ssd1306_WriteChar
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font6x8
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font7x10
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font11x18
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font16x26
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font16x24
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for Font16x15
    ssd1306_fonts.o(.constdata) refers to ssd1306_fonts.o(.constdata) for char_width
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_get) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_init) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_reset) refers to abort.o(.text) for abort
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rand.o(.emb_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000D) for __rt_lib_init_rand_2
    rand.o(.emb_text) refers to rand.o(.text) for _rand_init
    rand.o(.emb_text) refers to rand.o(.bss) for _random_number_data
    rand.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000D) for __rt_lib_init_rand_2
    rand.o(.text) refers to rand.o(.bss) for .bss
    rand.o(.bss) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000D) for __rt_lib_init_rand_2
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    libinit2.o(.ARM.Collect$$libinit$$0000000D) refers (Weak) to rand.o(.text) for _rand_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (76 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (64 bytes).
    Removing usart.o(i.fputc), (24 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (592 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (604 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(i.dancing_man_pause), (12 bytes).
    Removing oled_app.o(i.dancing_man_reset), (20 bytes).
    Removing oled_app.o(i.dancing_man_resume), (20 bytes).
    Removing oled_app.o(i.dancing_man_set_speed), (24 bytes).
    Removing oled_app.o(i.dancing_man_test), (80 bytes).
    Removing oled_app.o(i.oled_metaballs_task), (304 bytes).
    Removing oled_app.o(i.oled_test_animation_task), (120 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(.data), (1 bytes).
    Removing ssd1306.o(.rev16_text), (4 bytes).
    Removing ssd1306.o(.revsh_text), (4 bytes).
    Removing ssd1306.o(.rrx_text), (6 bytes).
    Removing ssd1306.o(i.ssd1306_DrawRectangle), (68 bytes).
    Removing ssd1306.o(i.ssd1306_FillCircle), (162 bytes).
    Removing ssd1306.o(i.ssd1306_FillRectangle), (86 bytes).
    Removing ssd1306.o(i.ssd1306_GetDisplayOn), (12 bytes).
    Removing ssd1306.o(i.ssd1306_Reset), (2 bytes).
    Removing ssd1306_fonts.o(.rev16_text), (4 bytes).
    Removing ssd1306_fonts.o(.revsh_text), (4 bytes).
    Removing ssd1306_fonts.o(.rrx_text), (6 bytes).
    Removing ssd1306_fonts.o(.constdata), (4940 bytes).
    Removing ssd1306_fonts.o(.constdata), (4560 bytes).
    Removing ssd1306_fonts.o(.constdata), (2850 bytes).
    Removing ssd1306_fonts.o(.constdata), (95 bytes).
    Removing ssd1306_fonts.o(.constdata), (12 bytes).
    Removing ssd1306_fonts.o(.constdata), (12 bytes).
    Removing ssd1306_fonts.o(.constdata), (12 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).

299 unused section(s) (total 32064 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  rand.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  rand.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\APP\oled_app.c                        0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\APP\uart_app.c                        0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\\APP\\oled_app.c                      0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\APP\\uart_app.c                      0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\components\\oled\\ssd1306.c          0x00000000   Number         0  ssd1306.o ABSOLUTE
    ..\\components\\oled\\ssd1306_fonts.c    0x00000000   Number         0  ssd1306_fonts.o ABSOLUTE
    ..\components\oled\ssd1306.c             0x00000000   Number         0  ssd1306.o ABSOLUTE
    ..\components\oled\ssd1306_fonts.c       0x00000000   Number         0  ssd1306_fonts.o ABSOLUTE
    ..\components\ringbuffer\ringbuffer.c    0x00000000   Number         0  ringbuffer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000160   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000166   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800016c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000172   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000178   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800017e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000184   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800018e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000194   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800019a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001a0   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001a6   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080001ac   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080001b2   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080001b8   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080001be   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080001c4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080001ca   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080001d4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080001da   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080001e0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080001e6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080001ec   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001f0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001f2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000D          0x080001f2   Section        4  libinit2.o(.ARM.Collect$$libinit$$0000000D)
    .ARM.Collect$$libinit$$0000000E          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001f6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001fc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080001fc   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000208   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000208   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000208   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000212   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000214   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000216   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000216   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000218   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000218   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000218   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800021e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800021e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000222   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000222   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800022a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800022c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800022c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000230   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000238   Section       52  rand.o(.emb_text)
    .text                                    0x0800026c   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x080002ac   Section        0  vsnprintf.o(.text)
    .text                                    0x080002e0   Section        0  __2sprintf.o(.text)
    .text                                    0x0800030c   Section        0  _printf_str.o(.text)
    .text                                    0x08000360   Section        0  _printf_dec.o(.text)
    .text                                    0x080003d8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000560   Section        0  rand.o(.text)
    .text                                    0x0800059c   Section        0  abort.o(.text)
    .text                                    0x080005b2   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800063c   Section       16  aeabi_memset.o(.text)
    .text                                    0x0800064c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800069a   Section        0  heapauxi.o(.text)
    .text                                    0x080006a0   Section        0  sys_exit.o(.text)
    .text                                    0x080006ac   Section        0  _printf_pad.o(.text)
    .text                                    0x080006fa   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800071e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080007d0   Section        0  _printf_charcount.o(.text)
    .text                                    0x080007f8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080007f9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000828   Section        0  _sputc.o(.text)
    .text                                    0x08000832   Section        0  _snputc.o(.text)
    .text                                    0x08000842   Section        0  _printf_char.o(.text)
    .text                                    0x08000870   Section        0  _printf_wctomb.o(.text)
    .text                                    0x0800092c   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x080009a8   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x080009a9   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000a18   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000a19   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000aac   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08000aba   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000b1e   Section       68  rt_memclr.o(.text)
    .text                                    0x08000b62   Section        2  use_no_semi.o(.text)
    .text                                    0x08000b64   Section        0  indicate_semi.o(.text)
    .text                                    0x08000b64   Section      138  lludiv10.o(.text)
    .text                                    0x08000bee   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000bf1   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800100c   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001308   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001334   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001374   Section        0  defsig_exit.o(.text)
    .text                                    0x08001380   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x080013b0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080013fc   Section       16  rt_ctype_table.o(.text)
    .text                                    0x0800140c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001414   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001494   Section        0  bigflt0.o(.text)
    .text                                    0x08001578   Section        0  exit.o(.text)
    .text                                    0x0800158a   Section        0  defsig_general.o(.text)
    .text                                    0x080015bc   Section        0  sys_wrch.o(.text)
    .text                                    0x080015cc   Section        8  libspace.o(.text)
    .text                                    0x080015d4   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08001654   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001692   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080016d8   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001738   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001a70   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001b4c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001b76   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001ba0   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08001de4   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel5_IRQHandler               0x08001de8   Section        0  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    i.DMA1_Channel6_IRQHandler               0x08001df4   Section        0  stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler)
    i.DMA_SetConfig                          0x08001e00   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001e01   Thumb Code    42  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08001e2a   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08001e2c   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08001e30   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001e78   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_GetState                       0x08001f10   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_GetState)
    i.HAL_DMA_IRQHandler                     0x08001f18   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x0800206c   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080020c8   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002138   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x0800215c   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GetTick                            0x0800233c   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_AbortCpltCallback              0x08002348   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    i.HAL_I2C_AddrCallback                   0x0800234a   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    i.HAL_I2C_ER_IRQHandler                  0x0800234c   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler)
    i.HAL_I2C_EV_IRQHandler                  0x08002410   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    i.HAL_I2C_ErrorCallback                  0x08002640   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    i.HAL_I2C_GetState                       0x08002642   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState)
    i.HAL_I2C_Init                           0x08002648   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_ListenCpltCallback             0x080027d0   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    i.HAL_I2C_MasterRxCpltCallback           0x080027d2   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback)
    i.HAL_I2C_MasterTxCpltCallback           0x080027d4   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback)
    i.HAL_I2C_MemRxCpltCallback              0x080027d6   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback)
    i.HAL_I2C_MemTxCpltCallback              0x080027d8   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback)
    i.HAL_I2C_Mem_Write                      0x080027dc   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_Mem_Write_DMA                  0x0800290c   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA)
    i.HAL_I2C_MspInit                        0x08002a9c   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_I2C_SlaveRxCpltCallback            0x08002b60   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    i.HAL_I2C_SlaveTxCpltCallback            0x08002b62   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    i.HAL_IncTick                            0x08002b64   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002b74   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002b98   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002bd8   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002c14   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002c30   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002c70   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002c94   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08002dc0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002de0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002e00   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002e4c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800316c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08003194   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x080031e0   Section        0  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x0800322c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x0800329c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080032a0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x0800350c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003570   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08003620   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x0800363c   Section        0  uart_app.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08003678   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x0800367a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x0800371a   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x0800371c   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.I2C1_ER_IRQHandler                     0x08003720   Section        0  stm32f1xx_it.o(i.I2C1_ER_IRQHandler)
    i.I2C1_EV_IRQHandler                     0x0800372c   Section        0  stm32f1xx_it.o(i.I2C1_EV_IRQHandler)
    i.I2C_DMAAbort                           0x08003738   Section        0  stm32f1xx_hal_i2c.o(i.I2C_DMAAbort)
    I2C_DMAAbort                             0x08003739   Thumb Code   182  stm32f1xx_hal_i2c.o(i.I2C_DMAAbort)
    i.I2C_DMAError                           0x080037f4   Section        0  stm32f1xx_hal_i2c.o(i.I2C_DMAError)
    I2C_DMAError                             0x080037f5   Thumb Code    54  stm32f1xx_hal_i2c.o(i.I2C_DMAError)
    i.I2C_DMAXferCplt                        0x0800382a   Section        0  stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt)
    I2C_DMAXferCplt                          0x0800382b   Thumb Code   274  stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt)
    i.I2C_Flush_DR                           0x0800393c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Flush_DR)
    I2C_Flush_DR                             0x0800393d   Thumb Code    16  stm32f1xx_hal_i2c.o(i.I2C_Flush_DR)
    i.I2C_ITError                            0x0800394c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_ITError)
    I2C_ITError                              0x0800394d   Thumb Code   336  stm32f1xx_hal_i2c.o(i.I2C_ITError)
    i.I2C_IsAcknowledgeFailed                0x08003aa4   Section        0  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08003aa5   Thumb Code    46  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterReceive_BTF                  0x08003ad2   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF)
    I2C_MasterReceive_BTF                    0x08003ad3   Thumb Code   218  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF)
    i.I2C_MasterReceive_RXNE                 0x08003bac   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE)
    I2C_MasterReceive_RXNE                   0x08003bad   Thumb Code   238  stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE)
    i.I2C_MasterTransmit_BTF                 0x08003ca0   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF)
    I2C_MasterTransmit_BTF                   0x08003ca1   Thumb Code   130  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF)
    i.I2C_MasterTransmit_TXE                 0x08003d24   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE)
    I2C_MasterTransmit_TXE                   0x08003d25   Thumb Code   182  stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE)
    i.I2C_Master_ADDR                        0x08003ddc   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR)
    I2C_Master_ADDR                          0x08003ddd   Thumb Code   340  stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR)
    i.I2C_Master_SB                          0x08003f34   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Master_SB)
    I2C_Master_SB                            0x08003f35   Thumb Code   140  stm32f1xx_hal_i2c.o(i.I2C_Master_SB)
    i.I2C_MemoryTransmit_TXE_BTF             0x08003fc0   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF)
    I2C_MemoryTransmit_TXE_BTF               0x08003fc1   Thumb Code   168  stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF)
    i.I2C_RequestMemoryWrite                 0x08004068   Section        0  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08004069   Thumb Code   162  stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_Slave_ADDR                         0x08004110   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR)
    I2C_Slave_ADDR                           0x08004111   Thumb Code    70  stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR)
    i.I2C_Slave_AF                           0x08004158   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Slave_AF)
    I2C_Slave_AF                             0x08004159   Thumb Code   138  stm32f1xx_hal_i2c.o(i.I2C_Slave_AF)
    i.I2C_Slave_STOPF                        0x080041e8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF)
    I2C_Slave_STOPF                          0x080041e9   Thumb Code   338  stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08004344   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004345   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x0800439c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x0800439d   Thumb Code   144  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x0800442c   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800442d   Thumb Code   188  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x080044e8   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x080044e9   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.MX_DMA_Init                            0x08004540   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x0800457c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x080045b8   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_USART1_UART_Init                    0x080045f8   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08004648   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800464a   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x0800464c   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x0800464e   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08004650   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08004654   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080046b2   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x080046b4   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080046b5   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080046c4   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080046c5   Thumb Code    74  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x0800470e   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x0800470f   Thumb Code   134  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08004794   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08004795   Thumb Code    30  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x080047b2   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080047b3   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08004800   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08004801   Thumb Code    28  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x0800481c   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800481d   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080048e0   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080048e1   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08004998   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08004a28   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08004a5e   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08004a5f   Thumb Code   114  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08004ad0   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08004adc   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08004ade   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x08004b06   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004b07   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x08004b26   Section        0  __printf_wp.o(i._is_digit)
    i.draw_dancing_man_frame                 0x08004b34   Section        0  oled_app.o(i.draw_dancing_man_frame)
    draw_dancing_man_frame                   0x08004b35   Thumb Code   136  oled_app.o(i.draw_dancing_man_frame)
    i.main                                   0x08004bbc   Section        0  main.o(i.main)
    i.metaballs_init                         0x08004c20   Section        0  oled_app.o(i.metaballs_init)
    i.my_printf                              0x08004d0c   Section        0  uart_app.o(i.my_printf)
    i.oled_app_init                          0x08004d40   Section        0  oled_app.o(i.oled_app_init)
    i.oled_dancing_man_task                  0x08004dc8   Section        0  oled_app.o(i.oled_dancing_man_task)
    i.rt_ringbuffer_data_len                 0x08004e94   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08004ec4   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08004f38   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08004f68   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08004fe0   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x08004fe1   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    i.scheduler_init                         0x08005000   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x0800500c   Section        0  scheduler.o(i.scheduler_run)
    i.ssd1306_DrawCircle                     0x08005048   Section        0  ssd1306.o(i.ssd1306_DrawCircle)
    i.ssd1306_DrawPixel                      0x0800510c   Section        0  ssd1306.o(i.ssd1306_DrawPixel)
    i.ssd1306_Fill                           0x0800513c   Section        0  ssd1306.o(i.ssd1306_Fill)
    i.ssd1306_Init                           0x08005154   Section        0  ssd1306.o(i.ssd1306_Init)
    i.ssd1306_Line                           0x0800521c   Section        0  ssd1306.o(i.ssd1306_Line)
    i.ssd1306_SetContrast                    0x0800529a   Section        0  ssd1306.o(i.ssd1306_SetContrast)
    i.ssd1306_SetCursor                      0x080052b0   Section        0  ssd1306.o(i.ssd1306_SetCursor)
    i.ssd1306_SetDisplayOn                   0x080052bc   Section        0  ssd1306.o(i.ssd1306_SetDisplayOn)
    i.ssd1306_UpdateScreen                   0x080052dc   Section        0  ssd1306.o(i.ssd1306_UpdateScreen)
    i.ssd1306_WriteChar                      0x08005314   Section        0  ssd1306.o(i.ssd1306_WriteChar)
    i.ssd1306_WriteCommand                   0x080053b4   Section        0  ssd1306.o(i.ssd1306_WriteCommand)
    i.ssd1306_WriteData                      0x080053d8   Section        0  ssd1306.o(i.ssd1306_WriteData)
    i.ssd1306_WriteString                    0x08005400   Section        0  ssd1306.o(i.ssd1306_WriteString)
    i.uart_task                              0x08005434   Section        0  uart_app.o(i.uart_task)
    locale$$code                             0x0800547c   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080054a8   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$fadd                               0x080054d4   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080054e3   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fdiv                               0x08005598   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08005599   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fflt                               0x0800571c   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fnaninf                            0x0800574c   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080057d8   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x080057e4   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x080057f3   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x080058ce   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x080058d2   Section        4  printf2.o(x$fpl$printf2)
    .constdata                               0x080058d6   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    x$fpl$usenofp                            0x080058d6   Section        0  usenofp.o(x$fpl$usenofp)
    aPredivFactorTable                       0x080058d6   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x080058d8   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x080058e8   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x080058f8   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x08005900   Section      144  oled_app.o(.constdata)
    dancing_frames                           0x08005900   Data         144  oled_app.o(.constdata)
    .constdata                               0x08005990   Section     1900  ssd1306_fonts.o(.constdata)
    Font7x10                                 0x08005990   Data        1900  ssd1306_fonts.o(.constdata)
    .constdata                               0x080060fc   Section     3420  ssd1306_fonts.o(.constdata)
    Font11x18                                0x080060fc   Data        3420  ssd1306_fonts.o(.constdata)
    .constdata                               0x08006e58   Section     1520  ssd1306_fonts.o(.constdata)
    Font6x8                                  0x08006e58   Data        1520  ssd1306_fonts.o(.constdata)
    .constdata                               0x08007448   Section       12  ssd1306_fonts.o(.constdata)
    .constdata                               0x08007454   Section       12  ssd1306_fonts.o(.constdata)
    .constdata                               0x08007460   Section       12  ssd1306_fonts.o(.constdata)
    .constdata                               0x0800746c   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800746c   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08007480   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08007480   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08007488   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08007488   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800749c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x080074b0   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080074b0   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080074c3   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080074d8   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080074d8   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08007514   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800758c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08007590   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08007598   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080075a4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080075a6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080075a7   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080075a8   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080075a8   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080075ac   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080075b4   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080076b8   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000010   Section       28  scheduler.o(.data)
    scheduler_task                           0x20000014   Data          24  scheduler.o(.data)
    .data                                    0x2000002c   Section       28  oled_app.o(.data)
    current_frame_index                      0x2000002c   Data           1  oled_app.o(.data)
    animation_paused                         0x2000002d   Data           1  oled_app.o(.data)
    show_frame_info                          0x2000002e   Data           1  oled_app.o(.data)
    current_fps                              0x20000030   Data           2  oled_app.o(.data)
    last_frame_time                          0x20000034   Data           4  oled_app.o(.data)
    frame_duration                           0x20000038   Data           4  oled_app.o(.data)
    frame_counter                            0x2000003c   Data           4  oled_app.o(.data)
    last_tick                                0x20000040   Data           4  oled_app.o(.data)
    current_time                             0x20000044   Data           4  oled_app.o(.data)
    .data                                    0x20000048   Section        8  uart_app.o(.data)
    .data                                    0x20000050   Section        6  ssd1306.o(.data)
    SSD1306                                  0x20000050   Data           6  ssd1306.o(.data)
    .bss                                     0x20000058   Section      152  i2c.o(.bss)
    .bss                                     0x200000f0   Section      140  usart.o(.bss)
    .bss                                     0x2000017c   Section       60  oled_app.o(.bss)
    balls                                    0x2000017c   Data          60  oled_app.o(.bss)
    .bss                                     0x200001b8   Section      396  uart_app.o(.bss)
    .bss                                     0x20000344   Section      128  uart_app.o(.bss)
    .bss                                     0x200003c4   Section     1024  ssd1306.o(.bss)
    .bss                                     0x200007c4   Section      228  rand.o(.bss)
    .bss                                     0x200008a8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000908   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20000908   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20000b08   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20000b08   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20000f08   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000161   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000167   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800016d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000173   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000179   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800017f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000185   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800018f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000195   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800019b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001a1   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001a7   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080001ad   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080001b3   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080001b9   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080001bf   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080001c5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080001cb   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080001d5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080001db   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080001e1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080001e7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080001ed   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001f1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_preinit_1                  0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_2                     0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000D)
    __rt_lib_init_user_alloc_1               0x080001f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x080001fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080001fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000209   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000209   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000209   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000215   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000217   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000219   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000219   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000219   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000223   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000223   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800022b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800022d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800022d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000231   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    rand                                     0x08000239   Thumb Code    48  rand.o(.emb_text)
    Reset_Handler                            0x0800026d   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x08000287   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x08000289   Thumb Code     0  startup_stm32f103xb.o(.text)
    vsnprintf                                0x080002ad   Thumb Code    48  vsnprintf.o(.text)
    __2sprintf                               0x080002e1   Thumb Code    38  __2sprintf.o(.text)
    _printf_str                              0x0800030d   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000361   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080003d9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    srand                                    0x08000561   Thumb Code    42  rand.o(.text)
    _rand_init                               0x0800058b   Thumb Code     4  rand.o(.text)
    abort                                    0x0800059d   Thumb Code    22  abort.o(.text)
    __aeabi_memcpy                           0x080005b3   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080005b3   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000619   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memset                           0x0800063d   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr4                          0x0800064d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800064d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800064d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000651   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800069b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800069d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800069f   Thumb Code     2  heapauxi.o(.text)
    _sys_exit                                0x080006a1   Thumb Code     8  sys_exit.o(.text)
    _printf_pre_padding                      0x080006ad   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080006d9   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080006fb   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800070d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_int_common                       0x0800071f   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_charcount                        0x080007d1   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000803   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000829   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000833   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000843   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000857   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000867   Thumb Code     8  _printf_char.o(.text)
    _printf_wctomb                           0x08000871   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x0800092d   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080009a9   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x080009eb   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000a03   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000a19   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000a6f   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000a8b   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000a97   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __rt_SIGABRT                             0x08000aad   Thumb Code    14  defsig_abrt_outer.o(.text)
    __aeabi_memcpy4                          0x08000abb   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000abb   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000abb   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000b03   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x08000b1f   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000b1f   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000b23   Thumb Code     0  rt_memclr.o(.text)
    __I$use$semihosting                      0x08000b63   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000b63   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000b65   Thumb Code     0  indicate_semi.o(.text)
    _ll_udiv10                               0x08000b65   Thumb Code   138  lludiv10.o(.text)
    __lib_sel_fp_printf                      0x08000bef   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000da1   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x0800100d   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_lcs_common                       0x08001309   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x0800131d   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x0800132d   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x08001335   Thumb Code    64  _wcrtomb.o(.text)
    __sig_exit                               0x08001375   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001381   Thumb Code    14  defsig_abrt_inner.o(.text)
    __user_setup_stackheap                   0x080013b1   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080013fd   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x0800140d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001415   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001495   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001579   Thumb Code    18  exit.o(.text)
    __default_signal_display                 0x0800158b   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x080015bd   Thumb Code    14  sys_wrch.o(.text)
    __user_libspace                          0x080015cd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080015cd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080015cd   Thumb Code     0  libspace.o(.text)
    strcmp                                   0x080015d5   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08001655   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001693   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080016d9   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001739   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001a71   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001b4d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001b77   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001ba1   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08001de5   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    DMA1_Channel5_IRQHandler                 0x08001de9   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel5_IRQHandler)
    DMA1_Channel6_IRQHandler                 0x08001df5   Thumb Code     6  stm32f1xx_it.o(i.DMA1_Channel6_IRQHandler)
    DebugMon_Handler                         0x08001e2b   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08001e2d   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08001e31   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001e79   Thumb Code   148  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_GetState                         0x08001f11   Thumb Code     6  stm32f1xx_hal_dma.o(i.HAL_DMA_GetState)
    HAL_DMA_IRQHandler                       0x08001f19   Thumb Code   316  stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x0800206d   Thumb Code    84  stm32f1xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080020c9   Thumb Code   112  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002139   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x0800215d   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GetTick                              0x0800233d   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_AbortCpltCallback                0x08002349   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    HAL_I2C_AddrCallback                     0x0800234b   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    HAL_I2C_ER_IRQHandler                    0x0800234d   Thumb Code   196  stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler)
    HAL_I2C_EV_IRQHandler                    0x08002411   Thumb Code   560  stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    HAL_I2C_ErrorCallback                    0x08002641   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    HAL_I2C_GetState                         0x08002643   Thumb Code     6  stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState)
    HAL_I2C_Init                             0x08002649   Thumb Code   376  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_ListenCpltCallback               0x080027d1   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    HAL_I2C_MasterRxCpltCallback             0x080027d3   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback)
    HAL_I2C_MasterTxCpltCallback             0x080027d5   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback)
    HAL_I2C_MemRxCpltCallback                0x080027d7   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback)
    HAL_I2C_MemTxCpltCallback                0x080027d9   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback)
    HAL_I2C_Mem_Write                        0x080027dd   Thumb Code   294  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_Mem_Write_DMA                    0x0800290d   Thumb Code   384  stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA)
    HAL_I2C_MspInit                          0x08002a9d   Thumb Code   170  i2c.o(i.HAL_I2C_MspInit)
    HAL_I2C_SlaveRxCpltCallback              0x08002b61   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    HAL_I2C_SlaveTxCpltCallback              0x08002b63   Thumb Code     2  stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    HAL_IncTick                              0x08002b65   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002b75   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002b99   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002bd9   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002c15   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002c31   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002c71   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002c95   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002dc1   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002de1   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002e01   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002e4d   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800316d   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003195   Thumb Code    74  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080031e1   Thumb Code    60  uart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x0800322d   Thumb Code   112  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x0800329d   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080032a1   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800350d   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003571   Thumb Code   154  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08003621   Thumb Code    28  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x0800363d   Thumb Code    38  uart_app.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003679   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800367b   Thumb Code   160  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x0800371b   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x0800371d   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    I2C1_ER_IRQHandler                       0x08003721   Thumb Code     6  stm32f1xx_it.o(i.I2C1_ER_IRQHandler)
    I2C1_EV_IRQHandler                       0x0800372d   Thumb Code     6  stm32f1xx_it.o(i.I2C1_EV_IRQHandler)
    MX_DMA_Init                              0x08004541   Thumb Code    56  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x0800457d   Thumb Code    54  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x080045b9   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_USART1_UART_Init                      0x080045f9   Thumb Code    66  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08004649   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800464b   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x0800464d   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x0800464f   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08004651   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08004655   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x080046b3   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    UART_Start_Receive_DMA                   0x08004999   Thumb Code   130  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08004a29   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08004ad1   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08004add   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08004adf   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08004b27   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x08004bbd   Thumb Code    58  main.o(i.main)
    metaballs_init                           0x08004c21   Thumb Code   228  oled_app.o(i.metaballs_init)
    my_printf                                0x08004d0d   Thumb Code    50  uart_app.o(i.my_printf)
    oled_app_init                            0x08004d41   Thumb Code   100  oled_app.o(i.oled_app_init)
    oled_dancing_man_task                    0x08004dc9   Thumb Code   156  oled_app.o(i.oled_dancing_man_task)
    rt_ringbuffer_data_len                   0x08004e95   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08004ec5   Thumb Code   116  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08004f39   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08004f69   Thumb Code   120  ringbuffer.o(i.rt_ringbuffer_put)
    scheduler_init                           0x08005001   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x0800500d   Thumb Code    56  scheduler.o(i.scheduler_run)
    ssd1306_DrawCircle                       0x08005049   Thumb Code   196  ssd1306.o(i.ssd1306_DrawCircle)
    ssd1306_DrawPixel                        0x0800510d   Thumb Code    42  ssd1306.o(i.ssd1306_DrawPixel)
    ssd1306_Fill                             0x0800513d   Thumb Code    18  ssd1306.o(i.ssd1306_Fill)
    ssd1306_Init                             0x08005155   Thumb Code   194  ssd1306.o(i.ssd1306_Init)
    ssd1306_Line                             0x0800521d   Thumb Code   126  ssd1306.o(i.ssd1306_Line)
    ssd1306_SetContrast                      0x0800529b   Thumb Code    20  ssd1306.o(i.ssd1306_SetContrast)
    ssd1306_SetCursor                        0x080052b1   Thumb Code     8  ssd1306.o(i.ssd1306_SetCursor)
    ssd1306_SetDisplayOn                     0x080052bd   Thumb Code    28  ssd1306.o(i.ssd1306_SetDisplayOn)
    ssd1306_UpdateScreen                     0x080052dd   Thumb Code    52  ssd1306.o(i.ssd1306_UpdateScreen)
    ssd1306_WriteChar                        0x08005315   Thumb Code   154  ssd1306.o(i.ssd1306_WriteChar)
    ssd1306_WriteCommand                     0x080053b5   Thumb Code    32  ssd1306.o(i.ssd1306_WriteCommand)
    ssd1306_WriteData                        0x080053d9   Thumb Code    36  ssd1306.o(i.ssd1306_WriteData)
    ssd1306_WriteString                      0x08005401   Thumb Code    52  ssd1306.o(i.ssd1306_WriteString)
    uart_task                                0x08005435   Thumb Code    52  uart_app.o(i.uart_task)
    _get_lc_numeric                          0x0800547d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080054a9   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_fadd                             0x080054d5   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x080054d5   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fdiv                             0x08005599   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08005599   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_i2f                              0x0800571d   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x0800571d   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __fpl_fnaninf                            0x0800574d   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080057d9   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x080057e5   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x080057e5   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x080058cf   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x080058d3   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x080058d6   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x080058e8   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x080058f8   Data           8  system_stm32f1xx.o(.constdata)
    Font_6x8                                 0x08007448   Data          12  ssd1306_fonts.o(.constdata)
    Font_7x10                                0x08007454   Data          12  ssd1306_fonts.o(.constdata)
    Font_11x18                               0x08007460   Data          12  ssd1306_fonts.o(.constdata)
    Region$$Table$$Base                      0x0800756c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800758c   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080075b5   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f1xx.o(.data)
    task_num                                 0x20000010   Data           1  scheduler.o(.data)
    uart_rx_index                            0x20000048   Data           2  uart_app.o(.data)
    uart_rx_ticks                            0x2000004c   Data           4  uart_app.o(.data)
    hi2c1                                    0x20000058   Data          84  i2c.o(.bss)
    hdma_i2c1_tx                             0x200000ac   Data          68  i2c.o(.bss)
    huart1                                   0x200000f0   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x20000138   Data          68  usart.o(.bss)
    uart_rx_buffer                           0x200001b8   Data         128  uart_app.o(.bss)
    uart_rx_dma_buffer                       0x20000238   Data         128  uart_app.o(.bss)
    uart_dma_buffer                          0x200002b8   Data         128  uart_app.o(.bss)
    uart_ringbuffer                          0x20000338   Data          12  uart_app.o(.bss)
    ringbuffer_pool                          0x20000344   Data         128  uart_app.o(.bss)
    SSD1306_Buffer                           0x200003c4   Data        1024  ssd1306.o(.bss)
    _random_number_data                      0x200007c4   Data         228  rand.o(.bss)
    __libspace_start                         0x200008a8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000908   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007710, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000076b8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         2846  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         3175    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         3177    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         3179    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000000   Code   RO         2830    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x08000160   0x00000006   Code   RO         2920    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000166   0x08000166   0x00000006   Code   RO         2922    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800016c   0x0800016c   0x00000006   Code   RO         2925    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000172   0x08000172   0x00000006   Code   RO         2926    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000178   0x08000178   0x00000006   Code   RO         2927    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800017e   0x0800017e   0x00000006   Code   RO         2928    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000184   0x08000184   0x0000000a   Code   RO         2933    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800018e   0x0800018e   0x00000006   Code   RO         2924    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000194   0x08000194   0x00000006   Code   RO         2828    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800019a   0x0800019a   0x00000006   Code   RO         2829    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001a0   0x080001a0   0x00000006   Code   RO         2923    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001a6   0x080001a6   0x00000006   Code   RO         2921    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO         2930    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080001b2   0x080001b2   0x00000006   Code   RO         2931    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080001b8   0x080001b8   0x00000006   Code   RO         2932    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080001be   0x080001be   0x00000006   Code   RO         2937    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080001c4   0x080001c4   0x00000006   Code   RO         2938    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080001ca   0x080001ca   0x0000000a   Code   RO         2934    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080001d4   0x080001d4   0x00000006   Code   RO         2919    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080001da   0x080001da   0x00000006   Code   RO         2827    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001e0   0x080001e0   0x00000006   Code   RO         2935    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080001e6   0x080001e6   0x00000006   Code   RO         2936    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080001ec   0x080001ec   0x00000004   Code   RO         2929    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001f0   0x080001f0   0x00000002   Code   RO         3102    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2948    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2950    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2953    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000000   Code   RO         2955    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001f2   0x080001f2   0x00000004   Code   RO         2956    .ARM.Collect$$libinit$$0000000D  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO         2957    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000006   Code   RO         2958    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         2960    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001fc   0x080001fc   0x0000000c   Code   RO         2961    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000208   0x08000208   0x00000000   Code   RO         2962    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000208   0x08000208   0x00000000   Code   RO         2964    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000208   0x08000208   0x0000000a   Code   RO         2965    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2966    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2968    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2970    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2972    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2974    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2976    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2978    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2980    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2984    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2986    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2988    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2990    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000002   Code   RO         2991    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000002   Code   RO         3154    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3156    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3158    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3160    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3163    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3166    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3168    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000000   Code   RO         3171    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000216   0x08000216   0x00000002   Code   RO         3172    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         2870    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000218   0x08000218   0x00000000   Code   RO         3011    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000218   0x08000218   0x00000006   Code   RO         3023    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         3013    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800021e   0x0800021e   0x00000004   Code   RO         3014    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000222   0x08000222   0x00000000   Code   RO         3016    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000222   0x08000222   0x00000008   Code   RO         3017    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800022a   0x0800022a   0x00000002   Code   RO         3114    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         3130    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800022c   0x0800022c   0x00000004   Code   RO         3131    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000230   0x08000230   0x00000006   Code   RO         3132    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000236   0x08000236   0x00000002   PAD
    0x08000238   0x08000238   0x00000034   Code   RO         2831    .emb_text           c_w.l(rand.o)
    0x0800026c   0x0800026c   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x080002ac   0x080002ac   0x00000034   Code   RO         2795    .text               c_w.l(vsnprintf.o)
    0x080002e0   0x080002e0   0x0000002c   Code   RO         2797    .text               c_w.l(__2sprintf.o)
    0x0800030c   0x0800030c   0x00000052   Code   RO         2803    .text               c_w.l(_printf_str.o)
    0x0800035e   0x0800035e   0x00000002   PAD
    0x08000360   0x08000360   0x00000078   Code   RO         2805    .text               c_w.l(_printf_dec.o)
    0x080003d8   0x080003d8   0x00000188   Code   RO         2824    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000560   0x08000560   0x0000003c   Code   RO         2832    .text               c_w.l(rand.o)
    0x0800059c   0x0800059c   0x00000016   Code   RO         2836    .text               c_w.l(abort.o)
    0x080005b2   0x080005b2   0x0000008a   Code   RO         2838    .text               c_w.l(rt_memcpy_v6.o)
    0x0800063c   0x0800063c   0x00000010   Code   RO         2840    .text               c_w.l(aeabi_memset.o)
    0x0800064c   0x0800064c   0x0000004e   Code   RO         2842    .text               c_w.l(rt_memclr_w.o)
    0x0800069a   0x0800069a   0x00000006   Code   RO         2844    .text               c_w.l(heapauxi.o)
    0x080006a0   0x080006a0   0x0000000c   Code   RO         2868    .text               c_w.l(sys_exit.o)
    0x080006ac   0x080006ac   0x0000004e   Code   RO         2871    .text               c_w.l(_printf_pad.o)
    0x080006fa   0x080006fa   0x00000024   Code   RO         2873    .text               c_w.l(_printf_truncate.o)
    0x0800071e   0x0800071e   0x000000b2   Code   RO         2875    .text               c_w.l(_printf_intcommon.o)
    0x080007d0   0x080007d0   0x00000028   Code   RO         2877    .text               c_w.l(_printf_charcount.o)
    0x080007f8   0x080007f8   0x00000030   Code   RO         2879    .text               c_w.l(_printf_char_common.o)
    0x08000828   0x08000828   0x0000000a   Code   RO         2881    .text               c_w.l(_sputc.o)
    0x08000832   0x08000832   0x00000010   Code   RO         2883    .text               c_w.l(_snputc.o)
    0x08000842   0x08000842   0x0000002c   Code   RO         2885    .text               c_w.l(_printf_char.o)
    0x0800086e   0x0800086e   0x00000002   PAD
    0x08000870   0x08000870   0x000000bc   Code   RO         2887    .text               c_w.l(_printf_wctomb.o)
    0x0800092c   0x0800092c   0x0000007c   Code   RO         2890    .text               c_w.l(_printf_longlong_dec.o)
    0x080009a8   0x080009a8   0x00000070   Code   RO         2896    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000a18   0x08000a18   0x00000094   Code   RO         2916    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000aac   0x08000aac   0x0000000e   Code   RO         2939    .text               c_w.l(defsig_abrt_outer.o)
    0x08000aba   0x08000aba   0x00000064   Code   RO         2943    .text               c_w.l(rt_memcpy_w.o)
    0x08000b1e   0x08000b1e   0x00000044   Code   RO         2945    .text               c_w.l(rt_memclr.o)
    0x08000b62   0x08000b62   0x00000002   Code   RO         3007    .text               c_w.l(use_no_semi.o)
    0x08000b64   0x08000b64   0x00000000   Code   RO         3009    .text               c_w.l(indicate_semi.o)
    0x08000b64   0x08000b64   0x0000008a   Code   RO         3027    .text               c_w.l(lludiv10.o)
    0x08000bee   0x08000bee   0x0000041e   Code   RO         3029    .text               c_w.l(_printf_fp_dec.o)
    0x0800100c   0x0800100c   0x000002fc   Code   RO         3031    .text               c_w.l(_printf_fp_hex.o)
    0x08001308   0x08001308   0x0000002c   Code   RO         3036    .text               c_w.l(_printf_wchar.o)
    0x08001334   0x08001334   0x00000040   Code   RO         3038    .text               c_w.l(_wcrtomb.o)
    0x08001374   0x08001374   0x0000000a   Code   RO         3040    .text               c_w.l(defsig_exit.o)
    0x0800137e   0x0800137e   0x00000002   PAD
    0x08001380   0x08001380   0x00000030   Code   RO         3042    .text               c_w.l(defsig_abrt_inner.o)
    0x080013b0   0x080013b0   0x0000004a   Code   RO         3048    .text               c_w.l(sys_stackheap_outer.o)
    0x080013fa   0x080013fa   0x00000002   PAD
    0x080013fc   0x080013fc   0x00000010   Code   RO         3050    .text               c_w.l(rt_ctype_table.o)
    0x0800140c   0x0800140c   0x00000008   Code   RO         3055    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001414   0x08001414   0x00000080   Code   RO         3057    .text               c_w.l(_printf_fp_infnan.o)
    0x08001494   0x08001494   0x000000e4   Code   RO         3059    .text               c_w.l(bigflt0.o)
    0x08001578   0x08001578   0x00000012   Code   RO         3087    .text               c_w.l(exit.o)
    0x0800158a   0x0800158a   0x00000032   Code   RO         3091    .text               c_w.l(defsig_general.o)
    0x080015bc   0x080015bc   0x0000000e   Code   RO         3107    .text               c_w.l(sys_wrch.o)
    0x080015ca   0x080015ca   0x00000002   PAD
    0x080015cc   0x080015cc   0x00000008   Code   RO         3111    .text               c_w.l(libspace.o)
    0x080015d4   0x080015d4   0x00000080   Code   RO         3127    .text               c_w.l(strcmpv7m.o)
    0x08001654   0x08001654   0x0000003e   Code   RO         3062    CL$$btod_d2e        c_w.l(btod.o)
    0x08001692   0x08001692   0x00000046   Code   RO         3064    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080016d8   0x080016d8   0x00000060   Code   RO         3063    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001738   0x08001738   0x00000338   Code   RO         3072    CL$$btod_div_common  c_w.l(btod.o)
    0x08001a70   0x08001a70   0x000000dc   Code   RO         3069    CL$$btod_e2e        c_w.l(btod.o)
    0x08001b4c   0x08001b4c   0x0000002a   Code   RO         3066    CL$$btod_ediv       c_w.l(btod.o)
    0x08001b76   0x08001b76   0x0000002a   Code   RO         3065    CL$$btod_emul       c_w.l(btod.o)
    0x08001ba0   0x08001ba0   0x00000244   Code   RO         3071    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001de4   0x08001de4   0x00000002   Code   RO          330    i.BusFault_Handler  stm32f1xx_it.o
    0x08001de6   0x08001de6   0x00000002   PAD
    0x08001de8   0x08001de8   0x0000000c   Code   RO          331    i.DMA1_Channel5_IRQHandler  stm32f1xx_it.o
    0x08001df4   0x08001df4   0x0000000c   Code   RO          332    i.DMA1_Channel6_IRQHandler  stm32f1xx_it.o
    0x08001e00   0x08001e00   0x0000002a   Code   RO         1313    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x08001e2a   0x08001e2a   0x00000002   Code   RO          333    i.DebugMon_Handler  stm32f1xx_it.o
    0x08001e2c   0x08001e2c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08001e30   0x08001e30   0x00000046   Code   RO         1314    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x08001e76   0x08001e76   0x00000002   PAD
    0x08001e78   0x08001e78   0x00000098   Code   RO         1315    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x08001f10   0x08001f10   0x00000006   Code   RO         1318    i.HAL_DMA_GetState  stm32f1xx_hal_dma.o
    0x08001f16   0x08001f16   0x00000002   PAD
    0x08001f18   0x08001f18   0x00000154   Code   RO         1319    i.HAL_DMA_IRQHandler  stm32f1xx_hal_dma.o
    0x0800206c   0x0800206c   0x0000005c   Code   RO         1320    i.HAL_DMA_Init      stm32f1xx_hal_dma.o
    0x080020c8   0x080020c8   0x00000070   Code   RO         1324    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x08002138   0x08002138   0x00000024   Code   RO          940    i.HAL_Delay         stm32f1xx_hal.o
    0x0800215c   0x0800215c   0x000001e0   Code   RO         1250    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x0800233c   0x0800233c   0x0000000c   Code   RO          944    i.HAL_GetTick       stm32f1xx_hal.o
    0x08002348   0x08002348   0x00000002   Code   RO          496    i.HAL_I2C_AbortCpltCallback  stm32f1xx_hal_i2c.o
    0x0800234a   0x0800234a   0x00000002   Code   RO          497    i.HAL_I2C_AddrCallback  stm32f1xx_hal_i2c.o
    0x0800234c   0x0800234c   0x000000c4   Code   RO          500    i.HAL_I2C_ER_IRQHandler  stm32f1xx_hal_i2c.o
    0x08002410   0x08002410   0x00000230   Code   RO          501    i.HAL_I2C_EV_IRQHandler  stm32f1xx_hal_i2c.o
    0x08002640   0x08002640   0x00000002   Code   RO          503    i.HAL_I2C_ErrorCallback  stm32f1xx_hal_i2c.o
    0x08002642   0x08002642   0x00000006   Code   RO          506    i.HAL_I2C_GetState  stm32f1xx_hal_i2c.o
    0x08002648   0x08002648   0x00000188   Code   RO          507    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x080027d0   0x080027d0   0x00000002   Code   RO          509    i.HAL_I2C_ListenCpltCallback  stm32f1xx_hal_i2c.o
    0x080027d2   0x080027d2   0x00000002   Code   RO          510    i.HAL_I2C_MasterRxCpltCallback  stm32f1xx_hal_i2c.o
    0x080027d4   0x080027d4   0x00000002   Code   RO          511    i.HAL_I2C_MasterTxCpltCallback  stm32f1xx_hal_i2c.o
    0x080027d6   0x080027d6   0x00000002   Code   RO          523    i.HAL_I2C_MemRxCpltCallback  stm32f1xx_hal_i2c.o
    0x080027d8   0x080027d8   0x00000002   Code   RO          524    i.HAL_I2C_MemTxCpltCallback  stm32f1xx_hal_i2c.o
    0x080027da   0x080027da   0x00000002   PAD
    0x080027dc   0x080027dc   0x00000130   Code   RO          528    i.HAL_I2C_Mem_Write  stm32f1xx_hal_i2c.o
    0x0800290c   0x0800290c   0x00000190   Code   RO          529    i.HAL_I2C_Mem_Write_DMA  stm32f1xx_hal_i2c.o
    0x08002a9c   0x08002a9c   0x000000c4   Code   RO          237    i.HAL_I2C_MspInit   i2c.o
    0x08002b60   0x08002b60   0x00000002   Code   RO          533    i.HAL_I2C_SlaveRxCpltCallback  stm32f1xx_hal_i2c.o
    0x08002b62   0x08002b62   0x00000002   Code   RO          534    i.HAL_I2C_SlaveTxCpltCallback  stm32f1xx_hal_i2c.o
    0x08002b64   0x08002b64   0x00000010   Code   RO          950    i.HAL_IncTick       stm32f1xx_hal.o
    0x08002b74   0x08002b74   0x00000024   Code   RO          951    i.HAL_Init          stm32f1xx_hal.o
    0x08002b98   0x08002b98   0x00000040   Code   RO          952    i.HAL_InitTick      stm32f1xx_hal.o
    0x08002bd8   0x08002bd8   0x0000003c   Code   RO          436    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08002c14   0x08002c14   0x0000001a   Code   RO         1410    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08002c2e   0x08002c2e   0x00000002   PAD
    0x08002c30   0x08002c30   0x00000040   Code   RO         1416    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002c70   0x08002c70   0x00000024   Code   RO         1417    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08002c94   0x08002c94   0x0000012c   Code   RO         1108    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08002dc0   0x08002dc0   0x00000020   Code   RO         1115    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08002de0   0x08002de0   0x00000020   Code   RO         1116    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08002e00   0x08002e00   0x0000004c   Code   RO         1117    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08002e4c   0x08002e4c   0x00000320   Code   RO         1120    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x0800316c   0x0800316c   0x00000028   Code   RO         1421    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08003194   0x08003194   0x0000004a   Code   RO         1920    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f1xx_hal_uart.o
    0x080031de   0x080031de   0x00000002   PAD
    0x080031e0   0x080031e0   0x0000004c   Code   RO         2473    i.HAL_UARTEx_RxEventCallback  uart_app.o
    0x0800322c   0x0800322c   0x00000070   Code   RO         1934    i.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x0800329c   0x0800329c   0x00000002   Code   RO         1936    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x0800329e   0x0800329e   0x00000002   PAD
    0x080032a0   0x080032a0   0x0000026c   Code   RO         1939    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x0800350c   0x0800350c   0x00000064   Code   RO         1940    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x08003570   0x08003570   0x000000b0   Code   RO          279    i.HAL_UART_MspInit  usart.o
    0x08003620   0x08003620   0x0000001c   Code   RO         1945    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x0800363c   0x0800363c   0x0000003c   Code   RO         2474    i.HAL_UART_RxCpltCallback  uart_app.o
    0x08003678   0x08003678   0x00000002   Code   RO         1947    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x0800367a   0x0800367a   0x000000a0   Code   RO         1948    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x0800371a   0x0800371a   0x00000002   Code   RO         1951    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x0800371c   0x0800371c   0x00000002   Code   RO          334    i.HardFault_Handler  stm32f1xx_it.o
    0x0800371e   0x0800371e   0x00000002   PAD
    0x08003720   0x08003720   0x0000000c   Code   RO          335    i.I2C1_ER_IRQHandler  stm32f1xx_it.o
    0x0800372c   0x0800372c   0x0000000c   Code   RO          336    i.I2C1_EV_IRQHandler  stm32f1xx_it.o
    0x08003738   0x08003738   0x000000bc   Code   RO          545    i.I2C_DMAAbort      stm32f1xx_hal_i2c.o
    0x080037f4   0x080037f4   0x00000036   Code   RO          546    i.I2C_DMAError      stm32f1xx_hal_i2c.o
    0x0800382a   0x0800382a   0x00000112   Code   RO          547    i.I2C_DMAXferCplt   stm32f1xx_hal_i2c.o
    0x0800393c   0x0800393c   0x00000010   Code   RO          548    i.I2C_Flush_DR      stm32f1xx_hal_i2c.o
    0x0800394c   0x0800394c   0x00000158   Code   RO          549    i.I2C_ITError       stm32f1xx_hal_i2c.o
    0x08003aa4   0x08003aa4   0x0000002e   Code   RO          550    i.I2C_IsAcknowledgeFailed  stm32f1xx_hal_i2c.o
    0x08003ad2   0x08003ad2   0x000000da   Code   RO          551    i.I2C_MasterReceive_BTF  stm32f1xx_hal_i2c.o
    0x08003bac   0x08003bac   0x000000f4   Code   RO          552    i.I2C_MasterReceive_RXNE  stm32f1xx_hal_i2c.o
    0x08003ca0   0x08003ca0   0x00000082   Code   RO          555    i.I2C_MasterTransmit_BTF  stm32f1xx_hal_i2c.o
    0x08003d22   0x08003d22   0x00000002   PAD
    0x08003d24   0x08003d24   0x000000b6   Code   RO          556    i.I2C_MasterTransmit_TXE  stm32f1xx_hal_i2c.o
    0x08003dda   0x08003dda   0x00000002   PAD
    0x08003ddc   0x08003ddc   0x00000158   Code   RO          557    i.I2C_Master_ADDR   stm32f1xx_hal_i2c.o
    0x08003f34   0x08003f34   0x0000008c   Code   RO          558    i.I2C_Master_SB     stm32f1xx_hal_i2c.o
    0x08003fc0   0x08003fc0   0x000000a8   Code   RO          559    i.I2C_MemoryTransmit_TXE_BTF  stm32f1xx_hal_i2c.o
    0x08004068   0x08004068   0x000000a8   Code   RO          561    i.I2C_RequestMemoryWrite  stm32f1xx_hal_i2c.o
    0x08004110   0x08004110   0x00000046   Code   RO          562    i.I2C_Slave_ADDR    stm32f1xx_hal_i2c.o
    0x08004156   0x08004156   0x00000002   PAD
    0x08004158   0x08004158   0x00000090   Code   RO          563    i.I2C_Slave_AF      stm32f1xx_hal_i2c.o
    0x080041e8   0x080041e8   0x0000015c   Code   RO          564    i.I2C_Slave_STOPF   stm32f1xx_hal_i2c.o
    0x08004344   0x08004344   0x00000056   Code   RO          565    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x0800439a   0x0800439a   0x00000002   PAD
    0x0800439c   0x0800439c   0x00000090   Code   RO          566    i.I2C_WaitOnFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x0800442c   0x0800442c   0x000000bc   Code   RO          567    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080044e8   0x080044e8   0x00000056   Code   RO          569    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x0800453e   0x0800453e   0x00000002   PAD
    0x08004540   0x08004540   0x0000003c   Code   RO          212    i.MX_DMA_Init       dma.o
    0x0800457c   0x0800457c   0x0000003c   Code   RO          188    i.MX_GPIO_Init      gpio.o
    0x080045b8   0x080045b8   0x00000040   Code   RO          238    i.MX_I2C1_Init      i2c.o
    0x080045f8   0x080045f8   0x00000050   Code   RO          280    i.MX_USART1_UART_Init  usart.o
    0x08004648   0x08004648   0x00000002   Code   RO          337    i.MemManage_Handler  stm32f1xx_it.o
    0x0800464a   0x0800464a   0x00000002   Code   RO          338    i.NMI_Handler       stm32f1xx_it.o
    0x0800464c   0x0800464c   0x00000002   Code   RO          339    i.PendSV_Handler    stm32f1xx_it.o
    0x0800464e   0x0800464e   0x00000002   Code   RO          340    i.SVC_Handler       stm32f1xx_it.o
    0x08004650   0x08004650   0x00000004   Code   RO          341    i.SysTick_Handler   stm32f1xx_it.o
    0x08004654   0x08004654   0x0000005e   Code   RO           14    i.SystemClock_Config  main.o
    0x080046b2   0x080046b2   0x00000002   Code   RO         2274    i.SystemInit        system_stm32f1xx.o
    0x080046b4   0x080046b4   0x00000010   Code   RO         1953    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x080046c4   0x080046c4   0x0000004a   Code   RO         1954    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x0800470e   0x0800470e   0x00000086   Code   RO         1955    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x08004794   0x08004794   0x0000001e   Code   RO         1957    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x080047b2   0x080047b2   0x0000004e   Code   RO         1963    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x08004800   0x08004800   0x0000001c   Code   RO         1964    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x0800481c   0x0800481c   0x000000c2   Code   RO         1965    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x080048de   0x080048de   0x00000002   PAD
    0x080048e0   0x080048e0   0x000000b8   Code   RO         1966    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08004998   0x08004998   0x00000090   Code   RO         1967    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x08004a28   0x08004a28   0x00000036   Code   RO         1968    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x08004a5e   0x08004a5e   0x00000072   Code   RO         1969    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08004ad0   0x08004ad0   0x0000000c   Code   RO          342    i.USART1_IRQHandler  stm32f1xx_it.o
    0x08004adc   0x08004adc   0x00000002   Code   RO          343    i.UsageFault_Handler  stm32f1xx_it.o
    0x08004ade   0x08004ade   0x00000028   Code   RO         3105    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08004b06   0x08004b06   0x00000020   Code   RO         1423    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08004b26   0x08004b26   0x0000000e   Code   RO         2817    i._is_digit         c_w.l(__printf_wp.o)
    0x08004b34   0x08004b34   0x00000088   Code   RO         2379    i.draw_dancing_man_frame  oled_app.o
    0x08004bbc   0x08004bbc   0x00000064   Code   RO           15    i.main              main.o
    0x08004c20   0x08004c20   0x000000ec   Code   RO         2380    i.metaballs_init    oled_app.o
    0x08004d0c   0x08004d0c   0x00000032   Code   RO         2475    i.my_printf         uart_app.o
    0x08004d3e   0x08004d3e   0x00000002   PAD
    0x08004d40   0x08004d40   0x00000088   Code   RO         2381    i.oled_app_init     oled_app.o
    0x08004dc8   0x08004dc8   0x000000cc   Code   RO         2382    i.oled_dancing_man_task  oled_app.o
    0x08004e94   0x08004e94   0x00000030   Code   RO         2712    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08004ec4   0x08004ec4   0x00000074   Code   RO         2713    i.rt_ringbuffer_get  ringbuffer.o
    0x08004f38   0x08004f38   0x00000030   Code   RO         2715    i.rt_ringbuffer_init  ringbuffer.o
    0x08004f68   0x08004f68   0x00000078   Code   RO         2717    i.rt_ringbuffer_put  ringbuffer.o
    0x08004fe0   0x08004fe0   0x00000020   Code   RO         2722    i.rt_ringbuffer_status  ringbuffer.o
    0x08005000   0x08005000   0x0000000c   Code   RO         2311    i.scheduler_init    scheduler.o
    0x0800500c   0x0800500c   0x0000003c   Code   RO         2312    i.scheduler_run     scheduler.o
    0x08005048   0x08005048   0x000000c4   Code   RO         2536    i.ssd1306_DrawCircle  ssd1306.o
    0x0800510c   0x0800510c   0x00000030   Code   RO         2537    i.ssd1306_DrawPixel  ssd1306.o
    0x0800513c   0x0800513c   0x00000018   Code   RO         2539    i.ssd1306_Fill      ssd1306.o
    0x08005154   0x08005154   0x000000c8   Code   RO         2543    i.ssd1306_Init      ssd1306.o
    0x0800521c   0x0800521c   0x0000007e   Code   RO         2544    i.ssd1306_Line      ssd1306.o
    0x0800529a   0x0800529a   0x00000014   Code   RO         2546    i.ssd1306_SetContrast  ssd1306.o
    0x080052ae   0x080052ae   0x00000002   PAD
    0x080052b0   0x080052b0   0x0000000c   Code   RO         2547    i.ssd1306_SetCursor  ssd1306.o
    0x080052bc   0x080052bc   0x00000020   Code   RO         2548    i.ssd1306_SetDisplayOn  ssd1306.o
    0x080052dc   0x080052dc   0x00000038   Code   RO         2549    i.ssd1306_UpdateScreen  ssd1306.o
    0x08005314   0x08005314   0x000000a0   Code   RO         2550    i.ssd1306_WriteChar  ssd1306.o
    0x080053b4   0x080053b4   0x00000024   Code   RO         2551    i.ssd1306_WriteCommand  ssd1306.o
    0x080053d8   0x080053d8   0x00000028   Code   RO         2552    i.ssd1306_WriteData  ssd1306.o
    0x08005400   0x08005400   0x00000034   Code   RO         2553    i.ssd1306_WriteString  ssd1306.o
    0x08005434   0x08005434   0x00000048   Code   RO         2476    i.uart_task         uart_app.o
    0x0800547c   0x0800547c   0x0000002c   Code   RO         3085    locale$$code        c_w.l(lc_numeric_c.o)
    0x080054a8   0x080054a8   0x0000002c   Code   RO         3119    locale$$code        c_w.l(lc_ctype_c.o)
    0x080054d4   0x080054d4   0x000000c4   Code   RO         2848    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08005598   0x08005598   0x00000184   Code   RO         2855    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x0800571c   0x0800571c   0x00000030   Code   RO         2859    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x0800574c   0x0800574c   0x0000008c   Code   RO         2994    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080057d8   0x080057d8   0x0000000a   Code   RO         2996    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080057e2   0x080057e2   0x00000002   PAD
    0x080057e4   0x080057e4   0x000000ea   Code   RO         2850    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x080058ce   0x080058ce   0x00000004   Code   RO         2998    x$fpl$printf1       fz_ws.l(printf1.o)
    0x080058d2   0x080058d2   0x00000004   Code   RO         3000    x$fpl$printf2       fz_ws.l(printf2.o)
    0x080058d6   0x080058d6   0x00000000   Code   RO         3006    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080058d6   0x080058d6   0x00000012   Data   RO         1121    .constdata          stm32f1xx_hal_rcc.o
    0x080058e8   0x080058e8   0x00000010   Data   RO         2275    .constdata          system_stm32f1xx.o
    0x080058f8   0x080058f8   0x00000008   Data   RO         2276    .constdata          system_stm32f1xx.o
    0x08005900   0x08005900   0x00000090   Data   RO         2386    .constdata          oled_app.o
    0x08005990   0x08005990   0x0000076c   Data   RO         2675    .constdata          ssd1306_fonts.o
    0x080060fc   0x080060fc   0x00000d5c   Data   RO         2676    .constdata          ssd1306_fonts.o
    0x08006e58   0x08006e58   0x000005f0   Data   RO         2678    .constdata          ssd1306_fonts.o
    0x08007448   0x08007448   0x0000000c   Data   RO         2682    .constdata          ssd1306_fonts.o
    0x08007454   0x08007454   0x0000000c   Data   RO         2683    .constdata          ssd1306_fonts.o
    0x08007460   0x08007460   0x0000000c   Data   RO         2684    .constdata          ssd1306_fonts.o
    0x0800746c   0x0800746c   0x00000011   Data   RO         2825    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800747d   0x0800747d   0x00000003   PAD
    0x08007480   0x08007480   0x00000008   Data   RO         2888    .constdata          c_w.l(_printf_wctomb.o)
    0x08007488   0x08007488   0x00000028   Data   RO         2917    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x080074b0   0x080074b0   0x00000026   Data   RO         3032    .constdata          c_w.l(_printf_fp_hex.o)
    0x080074d6   0x080074d6   0x00000002   PAD
    0x080074d8   0x080074d8   0x00000094   Data   RO         3060    .constdata          c_w.l(bigflt0.o)
    0x0800756c   0x0800756c   0x00000020   Data   RO         3173    Region$$Table       anon$$obj.o
    0x0800758c   0x0800758c   0x0000001c   Data   RO         3084    locale$$data        c_w.l(lc_numeric_c.o)
    0x080075a8   0x080075a8   0x00000110   Data   RO         3118    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080076b8, Size: 0x00000f08, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080076b8   0x0000000c   Data   RW          958    .data               stm32f1xx_hal.o
    0x2000000c   0x080076c4   0x00000004   Data   RW         2277    .data               system_stm32f1xx.o
    0x20000010   0x080076c8   0x0000001c   Data   RW         2313    .data               scheduler.o
    0x2000002c   0x080076e4   0x0000001c   Data   RW         2387    .data               oled_app.o
    0x20000048   0x08007700   0x00000008   Data   RW         2479    .data               uart_app.o
    0x20000050   0x08007708   0x00000006   Data   RW         2555    .data               ssd1306.o
    0x20000056   0x0800770e   0x00000002   PAD
    0x20000058        -       0x00000098   Zero   RW          239    .bss                i2c.o
    0x200000f0        -       0x0000008c   Zero   RW          282    .bss                usart.o
    0x2000017c        -       0x0000003c   Zero   RW         2385    .bss                oled_app.o
    0x200001b8        -       0x0000018c   Zero   RW         2477    .bss                uart_app.o
    0x20000344        -       0x00000080   Zero   RW         2478    .bss                uart_app.o
    0x200003c4        -       0x00000400   Zero   RW         2554    .bss                ssd1306.o
    0x200007c4        -       0x000000e4   Zero   RW         2833    .bss                c_w.l(rand.o)
    0x200008a8        -       0x00000060   Zero   RW         3112    .bss                c_w.l(libspace.o)
    0x20000908        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20000b08        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        60          4          0          0          0        726   dma.o
        60          6          0          0          0        779   gpio.o
       260         40          0          0        152       1698   i2c.o
       198         42          0          0          0     403520   main.o
       712         92        144         28         60       4003   oled_app.o
       364          0          0          0          0       5963   ringbuffer.o
        72          8          0         28          0       2068   scheduler.o
      1002         44          0          6       1024      10186   ssd1306.o
         0          0       6876          0          0       2164   ssd1306_fonts.o
        64         26        236          0       1536        792   startup_stm32f103xb.o
       164         28          0         12          0       5749   stm32f1xx_hal.o
       198         14          0          0          0      28831   stm32f1xx_hal_cortex.o
       814         36          0          0          0       5393   stm32f1xx_hal_dma.o
       480         34          0          0          0       2224   stm32f1xx_hal_gpio.o
      5460         88          0          0          0      31820   stm32f1xx_hal_i2c.o
        60          8          0          0          0        838   stm32f1xx_hal_msp.o
      1240         84         18          0          0       4996   stm32f1xx_hal_rcc.o
      2150         24          0          0          0      15460   stm32f1xx_hal_uart.o
        80         30          0          0          0       5810   stm32f1xx_it.o
         2          0         24          4          0       1059   system_stm32f1xx.o
       258         58          0          8        524       6042   uart_app.o
       256         36          0          0        140       1769   usart.o

    ----------------------------------------------------------------------
     13986        <USER>       <GROUP>         88       3436     541890   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
        16          0          0          0          0         68   aeabi_memset.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       112         18          0          0        228        160   rand.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
       430          8          0          0          0        168   faddsub_clz.o
       388         76          0          0          0         96   fdiv.o
        48          0          0          0          0         68   fflt_clz.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         4          0          0          0          0         68   printf2.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      8520        <USER>        <GROUP>          0        324       5728   Library Totals
        16          0          5          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7440        324        551          0        324       5040   c_w.l
      1024         88          0          0          0        620   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      8520        <USER>        <GROUP>          0        324       5728   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     22506       1114       7886         88       3760     537682   Grand Totals
     22506       1114       7886         88       3760     537682   ELF Image Totals
     22506       1114       7886         88          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                30392 (  29.68kB)
    Total RW  Size (RW Data + ZI Data)              3848 (   3.76kB)
    Total ROM Size (Code + RO Data + RW Data)      30480 (  29.77kB)

==============================================================================

