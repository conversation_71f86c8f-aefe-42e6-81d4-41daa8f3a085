// �ļ���: oled_app.c

#include "oled_app.h"       // �����Լ���ͷ�ļ�
#include "main.h"           // ���� HAL �⣬Ϊ��ʹ�� HAL_GetTick() �� HAL_Delay()
#include "ssd1306.h"        // ���� OLED �ײ�������
#include "ssd1306_fonts.h"  // ���������
#include <stdio.h>          // ������׼��������⣬Ϊ��ʹ�� sprintf
#include <stdlib.h> // ��Ҫ�������ļ���ʹ�� rand() �� srand()
#include <time.h>   // �Ƽ���������Ȼֻ�� HAL_GetTick() Ҳ����
#include <math.h> // ��Ҫ���� math.h ��ʹ�� sqrtf
/**
 * @brief  OLED ˢ���ʼ��޲�������
 * @note   �˺���ּ��ͨ����ÿһ֡�ػ�������Ļ��������ʾ�����ݴ�������ޡ�
 *         ������ʾȫ���������㣨ѩ��Ч�����Լ�һ��ʵʱ�� FPS ��������
 *         Ҫʹ����������������ѭ����������е��ô˺����������� oled_refresh_task �� oled_animation_task��
 */
#define NUM_METABALLS 3 // ������������Գ����޸�Ϊ 2, 3, �� 4

// Ԫ��ṹ��
typedef struct {
    float x;         // x ����
    float y;         // y ����
    float vx;        // x �����ٶ�
    float vy;        // y �����ٶ�
    float r;         // �뾶
} Metaball;

// ����Ԫ������
static Metaball balls[NUM_METABALLS];
void oled_test_animation_task(void) {
    // --- 1. FPS (ÿ��֡��) �������߼� ---
    static uint32_t frame_counter = 0;
    static uint32_t last_tick = 0;
    static uint16_t current_fps = 0;
    char text_buffer[16];

    frame_counter++;
    uint32_t current_tick = HAL_GetTick();

    // ÿ���Ӹ���һ�� FPS ��ʾֵ
    if (current_tick - last_tick >= 1000) {
        current_fps = frame_counter; // ��ȥһ���ڻ��Ƶ���֡��
        frame_counter = 0;           // ����֡������
        last_tick = current_tick;    // ����ʱ���
    }


    // --- 2. ���ƺ��ģ�������������������Ļ������ ---
    // ���ǲ���ˢ��������Ч�ķ�������Ϊ��ǿ��ÿ�ζ�����������������
    // ����ֱ�Ӳ����ײ�� SSD1306_Buffer ���飬�������Ч�ķ�ʽ��
    // ע�⣺�������ǲ��ٵ��� ssd1306_Fill(Black)����Ϊ�����ѭ���Ḳ���������ء�
    extern uint8_t SSD1306_Buffer[]; // �� ssd1306.c ������Ļ����������
    for (uint16_t i = 0; i < SSD1306_BUFFER_SIZE; i++) {
        SSD1306_Buffer[i] = rand() % 256; // �� 0-255 ����������
    }


    // --- 3. �����֮�ϻ��� FPS �ı� ---
    // ssd1306_WriteString ����ƴ�����ɫ���ַ������Ϊ��������㱳����
    // ����һ�������ġ��ڿ򡱣�ʹ���ֿɶ���
    sprintf(text_buffer, "FPS: %u", current_fps);
    ssd1306_SetCursor(2, 2); // �������Ͻ�
    ssd1306_WriteString(text_buffer, Font_7x10, White);


    // --- 4. �����������ݸ��µ���Ļ ---
    // ��һ�������� I2C �� DMA ���䣨����Ѱ�֮ǰ���۵��޸ģ�
    ssd1306_UpdateScreen();
}

// ... oled_app.c �ļ��е��������� ...
/**
 * @brief  OLED Ӧ�ó�ʼ������
 */


// ======================================================================
// --- ��Metaballs / ����˿������ ---
// ======================================================================


// --- 1. ����Ԫ������ݽṹ��ȫ�ֱ��� ---



/**
 * @brief ��ʼ�� Metaballs ����
 * @note  �������Ӧ���� oled_app_init ��ĩβ������һ�Σ�
 *        �����ڿ�ʼ����ǰ���á���������ÿ����ĳ�ʼλ�á��ٶȺʹ�С��
 */
void metaballs_init(void) {
    // ʹ�� srand(HAL_GetTick()) �� oled_app_init ���Ѿ����ù�������

    for (int i = 0; i < NUM_METABALLS; i++) {
        // ��ʼλ������Ļ���븽��
        balls[i].x = SSD1306_WIDTH / 2.0f + (rand() % 20 - 10);
        balls[i].y = SSD1306_HEIGHT / 2.0f + (rand() % 20 - 10);

        // �����ʼ�ٶ�
        balls[i].vx = (float)(rand() % 100) / 100.0f + 0.5f; // �ٶ��� 0.5 �� 1.5 ֮��
        if (rand() % 2) balls[i].vx *= -1; // �������
        balls[i].vy = (float)(rand() % 100) / 100.0f + 0.5f;
        if (rand() % 2) balls[i].vy *= -1;

        // ����뾶
        balls[i].r = (float)(rand() % 5) + 8.0f; // �뾶�� 8 �� 13 ֮��
    }
}


/**
 * @brief Metaballs ����ˢ������
 * @note  ����ѭ����������ٶȵ��ô˺�����
 */
void oled_metaballs_task(void) {
    // --- 1. ����ÿ�����λ�ú��ٶ� (����ģ��) ---
    for (int i = 0; i < NUM_METABALLS; i++) {
        balls[i].x += balls[i].vx;
        balls[i].y += balls[i].vy;

        // ��ײ����뷴��
        if (balls[i].x < balls[i].r || balls[i].x > SSD1306_WIDTH - balls[i].r) {
            balls[i].vx *= -1; // x �ٶȷ���
        }
        if (balls[i].y < balls[i].r || balls[i].y > SSD1306_HEIGHT - balls[i].r) {
            balls[i].vy *= -1; // y �ٶȷ���
        }
    }

    // --- 2. ���ƺ��ģ�����ÿ�����صġ�����ֵ�� ---
    // ���� Metaballs �㷨�ĺ��ġ����Ǳ�����Ļ�ϵ�ÿ�����أ�
    // ����������Ը����ز����ġ��������ܺ͡�
    // ��ʽ�ǣ�sum(radius^2 / ((px - ball.x)^2 + (py - ball.y)^2))
    // ��������ܺʹ���ĳ����ֵ������1.0�������Ǿ͵���������ء�
    
    // �����Ļ������
    ssd1306_Fill(Black);

    for (int y = 0; y < SSD1306_HEIGHT; y++) {
        for (int x = 0; x < SSD1306_WIDTH; x++) {
            float sum = 0.0f;
            for (int i = 0; i < NUM_METABALLS; i++) {
                float dx = x - balls[i].x;
                float dy = y - balls[i].y;
                float dist_sq = dx * dx + dy * dy;

                // ���������
                if (dist_sq == 0.0f) {
                    sum += 1000.0f; // һ���ܴ��ֵ
                } else {
                    sum += (balls[i].r * balls[i].r) / dist_sq;
                }
            }

            // ��������ܺʹ�����ֵ����������
            if (sum >= 1.0f) {
                ssd1306_DrawPixel(x, y, White);
            }
        }
    }
    
    // --- 3. ���µ���Ļ ---
    ssd1306_UpdateScreen();
}
void oled_app_init(void) {
    // ���ÿ⺯����ʼ��OLEDӲ�����ڲ�״̬
    ssd1306_Init();

    // --- ��ʾ��ӭ���� ---
    // (��ԭ���Ļ�ӭ������뱣�ֲ���)
    ssd1306_Fill(Black);
    ssd1306_SetCursor(5, 8);
    ssd1306_WriteString("STM32 Ready", Font_11x18, White);
    ssd1306_Line(0, 30, SSD1306_WIDTH - 1, 30, White);
    ssd1306_SetCursor(18, 40);
    ssd1306_WriteString("OLED Init OK", Font_7x10, White);
    ssd1306_UpdateScreen();
    HAL_Delay(2000);

    // !! �������룺��ʼ����������� !!
    // ʹ��ϵͳ������ĺ�������Ϊ���ӣ�ȷ��ÿ�ε�������ж���ͬ
    srand(HAL_GetTick());
	metaballs_init();
}
