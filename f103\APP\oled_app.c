// �ļ���: oled_app.c

#include "oled_app.h"      // �����Լ���ͷ�ļ�
#include "main.h"          // ���� HAL �⣬Ϊ��ʹ�� HAL_GetTick() �� HAL_Delay()
#include "ssd1306.h"       // ���� OLED �ײ�������
#include "ssd1306_fonts.h" // ���������
#include <stdio.h>         // ������׼��������⣬Ϊ��ʹ�� sprintf
#include <stdlib.h>        // ��Ҫ�������ļ���ʹ�� rand() �� srand()
#include <stdint.h>        // 标准整数类型定义
#include <time.h>          // �Ƽ���������Ȼֻ�� HAL_GetTick() Ҳ����
#include <math.h>          // ��Ҫ���� math.h ��ʹ�� sqrtf
/**
 * @brief  OLED ˢ���ʼ��޲�������
 * @note   �˺���ּ��ͨ����ÿһ֡�ػ�������Ļ��������ʾ�����ݴ�������ޡ�
 *         ������ʾȫ���������㣨ѩ��Ч�����Լ�һ��ʵʱ�� FPS ��������
 *         Ҫʹ����������������ѭ����������е��ô˺����������� oled_refresh_task �� oled_animation_task��
 */
#define NUM_METABALLS 3 // ������������Գ����޸�Ϊ 2, 3, �� 4

// Ԫ��ṹ��
typedef struct
{
    float x;  // x ����
    float y;  // y ����
    float vx; // x �����ٶ�
    float vy; // y �����ٶ�
    float r;  // �뾶
} Metaball;

// ����Ԫ������
static Metaball balls[NUM_METABALLS];
void oled_test_animation_task(void)
{
    // --- 1. FPS (ÿ��֡��) �������߼� ---
    static uint32_t frame_counter = 0;
    static uint32_t last_tick = 0;
    static uint16_t current_fps = 0;
    char text_buffer[16];

    frame_counter++;
    uint32_t current_tick = HAL_GetTick();

    // ÿ���Ӹ���һ�� FPS ��ʾֵ
    if (current_tick - last_tick >= 1000)
    {
        current_fps = frame_counter; // ��ȥһ���ڻ��Ƶ���֡��
        frame_counter = 0;           // ����֡������
        last_tick = current_tick;    // ����ʱ���
    }

    // --- 2. ���ƺ��ģ�������������������Ļ������ ---
    // ���ǲ���ˢ��������Ч�ķ�������Ϊ��ǿ��ÿ�ζ�����������������
    // ����ֱ�Ӳ����ײ�� SSD1306_Buffer ���飬�������Ч�ķ�ʽ��
    // ע�⣺�������ǲ��ٵ��� ssd1306_Fill(Black)����Ϊ�����ѭ���Ḳ���������ء�
    extern uint8_t SSD1306_Buffer[]; // �� ssd1306.c ������Ļ����������
    for (uint16_t i = 0; i < SSD1306_BUFFER_SIZE; i++)
    {
        SSD1306_Buffer[i] = rand() % 256; // �� 0-255 ����������
    }

    // --- 3. �����֮�ϻ��� FPS �ı� ---
    // ssd1306_WriteString ����ƴ�����ɫ���ַ������Ϊ��������㱳����
    // ����һ�������ġ��ڿ򡱣�ʹ���ֿɶ���
    sprintf(text_buffer, "FPS: %u", current_fps);
    ssd1306_SetCursor(2, 2); // �������Ͻ�
    ssd1306_WriteString(text_buffer, Font_7x10, White);

    // --- 4. �����������ݸ��µ���Ļ ---
    // ��һ�������� I2C �� DMA ���䣨����Ѱ�֮ǰ���۵��޸ģ�
    ssd1306_UpdateScreen();
}

// ... oled_app.c �ļ��е��������� ...
/**
 * @brief  OLED Ӧ�ó�ʼ������
 */

// ======================================================================
// --- ��Metaballs / ����˿������ ---
// ======================================================================

// --- 1. ����Ԫ������ݽṹ��ȫ�ֱ��� ---

/**
 * @brief ��ʼ�� Metaballs ����
 * @note  �������Ӧ���� oled_app_init ��ĩβ������һ�Σ�
 *        �����ڿ�ʼ����ǰ���á���������ÿ����ĳ�ʼλ�á��ٶȺʹ�С��
 */
void metaballs_init(void)
{
    // ʹ�� srand(HAL_GetTick()) �� oled_app_init ���Ѿ����ù�������

    for (int i = 0; i < NUM_METABALLS; i++)
    {
        // ��ʼλ������Ļ���븽��
        balls[i].x = SSD1306_WIDTH / 2.0f + (rand() % 20 - 10);
        balls[i].y = SSD1306_HEIGHT / 2.0f + (rand() % 20 - 10);

        // �����ʼ�ٶ�
        balls[i].vx = (float)(rand() % 100) / 100.0f + 0.5f; // �ٶ��� 0.5 �� 1.5 ֮��
        if (rand() % 2)
            balls[i].vx *= -1; // �������
        balls[i].vy = (float)(rand() % 100) / 100.0f + 0.5f;
        if (rand() % 2)
            balls[i].vy *= -1;

        // ����뾶
        balls[i].r = (float)(rand() % 5) + 8.0f; // �뾶�� 8 �� 13 ֮��
    }
}

/**
 * @brief Metaballs ����ˢ������
 * @note  ����ѭ����������ٶȵ��ô˺�����
 */
void oled_metaballs_task(void)
{
    // --- 1. ����ÿ�����λ�ú��ٶ� (����ģ��) ---
    for (int i = 0; i < NUM_METABALLS; i++)
    {
        balls[i].x += balls[i].vx;
        balls[i].y += balls[i].vy;

        // ��ײ����뷴��
        if (balls[i].x < balls[i].r || balls[i].x > SSD1306_WIDTH - balls[i].r)
        {
            balls[i].vx *= -1; // x �ٶȷ���
        }
        if (balls[i].y < balls[i].r || balls[i].y > SSD1306_HEIGHT - balls[i].r)
        {
            balls[i].vy *= -1; // y �ٶȷ���
        }
    }

    // --- 2. ���ƺ��ģ�����ÿ�����صġ�����ֵ�� ---
    // ���� Metaballs �㷨�ĺ��ġ����Ǳ�����Ļ�ϵ�ÿ�����أ�
    // ����������Ը����ز����ġ��������ܺ͡�
    // ��ʽ�ǣ�sum(radius^2 / ((px - ball.x)^2 + (py - ball.y)^2))
    // ��������ܺʹ���ĳ����ֵ������1.0�������Ǿ͵���������ء�

    // �����Ļ������
    ssd1306_Fill(Black);

    for (int y = 0; y < SSD1306_HEIGHT; y++)
    {
        for (int x = 0; x < SSD1306_WIDTH; x++)
        {
            float sum = 0.0f;
            for (int i = 0; i < NUM_METABALLS; i++)
            {
                float dx = x - balls[i].x;
                float dy = y - balls[i].y;
                float dist_sq = dx * dx + dy * dy;

                // ���������
                if (dist_sq == 0.0f)
                {
                    sum += 1000.0f; // һ���ܴ��ֵ
                }
                else
                {
                    sum += (balls[i].r * balls[i].r) / dist_sq;
                }
            }

            // ��������ܺʹ�����ֵ����������
            if (sum >= 1.0f)
            {
                ssd1306_DrawPixel(x, y, White);
            }
        }
    }

    // --- 3. ���µ���Ļ ---
    ssd1306_UpdateScreen();
}
void oled_app_init(void)
{
    // ���ÿ⺯����ʼ��OLEDӲ�����ڲ�״̬
    ssd1306_Init();

    // --- ��ʾ��ӭ���� ---
    // (��ԭ���Ļ�ӭ������뱣�ֲ���)
    ssd1306_Fill(Black);
    ssd1306_SetCursor(5, 8);
    ssd1306_WriteString("STM32 Ready", Font_11x18, White);
    ssd1306_Line(0, 30, SSD1306_WIDTH - 1, 30, White);
    ssd1306_SetCursor(18, 40);
    ssd1306_WriteString("OLED Init OK", Font_7x10, White);
    ssd1306_UpdateScreen();
    HAL_Delay(2000);

    // !! �������룺��ʼ����������� !!
    // ʹ��ϵͳ������ĺ�������Ϊ���ӣ�ȷ��ÿ�ε�������ж���ͬ
    srand(HAL_GetTick());
    metaballs_init();
}

// ======================================================================
// --- 小人跳舞动画实现 ---
// ======================================================================

// 定义6帧跳舞动画序列
static const DancingManFrame dancing_frames[6] = {
    // 帧1 - 基本站立姿态
    {
        .head = {64, 20, 4},           // 头部：圆心(64,20), 半径4
        .body = {64, 24, 64, 44},      // 身体：垂直线条
        .left_arm = {64, 30, 54, 35},  // 左臂：向左下
        .right_arm = {64, 30, 74, 35}, // 右臂：向右下
        .left_leg = {64, 44, 59, 59},  // 左腿：向左下
        .right_leg = {64, 44, 69, 59}, // 右腿：向右下
        .y_offset = 0                  // 无偏移
    },
    // 帧2 - 左腿抬起，右手上举
    {
        .head = {64, 20, 4},
        .body = {64, 24, 64, 44},
        .left_arm = {64, 30, 54, 35},  // 左臂保持
        .right_arm = {64, 30, 74, 20}, // 右臂上举
        .left_leg = {64, 44, 54, 50},  // 左腿抬起
        .right_leg = {64, 44, 69, 59}, // 右腿支撑
        .y_offset = 0},
    // 帧3 - 双手上举，轻微跳跃
    {
        .head = {64, 18, 4}, // 整体上移2像素
        .body = {64, 22, 64, 42},
        .left_arm = {64, 28, 54, 18},  // 左臂上举
        .right_arm = {64, 28, 74, 18}, // 右臂上举
        .left_leg = {64, 42, 59, 55},  // 双腿收缩
        .right_leg = {64, 42, 69, 55},
        .y_offset = -2 // 跳跃偏移
    },
    // 帧4 - 右腿抬起，左手上举
    {
        .head = {64, 20, 4},
        .body = {64, 24, 64, 44},
        .left_arm = {64, 30, 54, 20},  // 左臂上举
        .right_arm = {64, 30, 74, 35}, // 右臂保持
        .left_leg = {64, 44, 59, 59},  // 左腿支撑
        .right_leg = {64, 44, 74, 50}, // 右腿抬起
        .y_offset = 0},
    // 帧5 - 双手向两侧伸展
    {
        .head = {64, 20, 4},
        .body = {64, 24, 64, 44},
        .left_arm = {64, 30, 49, 30},  // 左臂水平伸展
        .right_arm = {64, 30, 79, 30}, // 右臂水平伸展
        .left_leg = {64, 44, 59, 59},
        .right_leg = {64, 44, 69, 59},
        .y_offset = 0},
    // 帧6 - 回到基本站立姿态（与帧1相同）
    {
        .head = {64, 20, 4},
        .body = {64, 24, 64, 44},
        .left_arm = {64, 30, 54, 35},
        .right_arm = {64, 30, 74, 35},
        .left_leg = {64, 44, 59, 59},
        .right_leg = {64, 44, 69, 59},
        .y_offset = 0}};

/**
 * @brief 绘制小人的单帧画面
 * @param frame 要绘制的帧数据
 */
static void draw_dancing_man_frame(const DancingManFrame *frame)
{
    // 应用Y轴偏移
    int8_t offset = frame->y_offset;

    // 绘制头部（圆形）
    ssd1306_DrawCircle(frame->head.x, frame->head.y + offset, frame->head.radius, White);

    // 绘制身体（垂直线条）
    ssd1306_Line(frame->body.x1, frame->body.y1 + offset,
                 frame->body.x2, frame->body.y2 + offset, White);

    // 绘制左臂
    ssd1306_Line(frame->left_arm.x1, frame->left_arm.y1 + offset,
                 frame->left_arm.x2, frame->left_arm.y2 + offset, White);

    // 绘制右臂
    ssd1306_Line(frame->right_arm.x1, frame->right_arm.y1 + offset,
                 frame->right_arm.x2, frame->right_arm.y2 + offset, White);

    // 绘制左腿
    ssd1306_Line(frame->left_leg.x1, frame->left_leg.y1 + offset,
                 frame->left_leg.x2, frame->left_leg.y2 + offset, White);

    // 绘制右腿
    ssd1306_Line(frame->right_leg.x1, frame->right_leg.y1 + offset,
                 frame->right_leg.x2, frame->right_leg.y2 + offset, White);
}

// 动画控制变量
static uint8_t current_frame_index = 0; // 当前帧索引
static uint32_t last_frame_time = 0;    // 上次帧切换时间
static uint32_t frame_duration = 180;   // 每帧持续时间(ms) - 可调节
static const uint8_t total_frames = 6;  // 总帧数
static uint8_t animation_paused = 0;    // 动画暂停标志
static uint8_t show_frame_info = 1;     // 是否显示帧信息

/**
 * @brief 小人跳舞动画任务函数
 * @note 在调度器中定期调用，实现流畅的跳舞动画
 */
void oled_dancing_man_task(void)
{
    static uint32_t current_time;
    current_time = HAL_GetTick();

    // 检查动画是否暂停
    if (!animation_paused)
    {
        // 检查是否需要切换到下一帧
        if (current_time - last_frame_time >= frame_duration)
        {
            // 更新帧索引，实现循环播放
            current_frame_index = (current_frame_index + 1) % total_frames;
            last_frame_time = current_time;
        }
    }

    // 清空屏幕
    ssd1306_Fill(Black);

    // 绘制当前帧的小人
    draw_dancing_man_frame(&dancing_frames[current_frame_index]);

    // 可选：显示帧信息和状态
    if (show_frame_info)
    {
        char info_buffer[32];
        sprintf(info_buffer, "F:%d %s", current_frame_index + 1,
                animation_paused ? "PAUSE" : "PLAY");
        ssd1306_SetCursor(2, 2);
        ssd1306_WriteString(info_buffer, Font_6x8, White);

        // 显示速度信息
        sprintf(info_buffer, "Speed:%dms", (int)frame_duration);
        ssd1306_SetCursor(2, 12);
        ssd1306_WriteString(info_buffer, Font_6x8, White);
    }

    // 更新屏幕显示
    ssd1306_UpdateScreen();
}

// ======================================================================
// --- 动画控制函数实现 ---
// ======================================================================

/**
 * @brief 暂停动画
 */
void dancing_man_pause(void)
{
    animation_paused = 1;
}

/**
 * @brief 恢复动画
 */
void dancing_man_resume(void)
{
    animation_paused = 0;
    last_frame_time = HAL_GetTick(); // 重置时间，避免跳帧
}

/**
 * @brief 重置动画到第一帧
 */
void dancing_man_reset(void)
{
    current_frame_index = 0;
    last_frame_time = HAL_GetTick();
}

/**
 * @brief 设置动画速度
 * @param speed_ms 每帧间隔时间(毫秒)
 */
void dancing_man_set_speed(uint32_t speed_ms)
{
    if (speed_ms >= 50 && speed_ms <= 1000)
    { // 限制速度范围
        frame_duration = speed_ms;
    }
}
