/**
 * Private configuration file for the SSD1306 library.
 * This example is configured for STM32F0, I2C and including all fonts.
 */

#ifndef __SSD1306_CONF_H__
#define __SSD1306_CONF_H__

/*
================================================================================
/                                                                              /
/                      SSD1306 Configuration File                              /
/                                                                              /
================================================================================
/                                                                              /
/   This file is a configuration for the SSD1306 OLED library.                 /
/   You should copy `ssd1306_conf_template.h` and rename it to `ssd1306_conf.h`/
/   and then edit it to match your project setup.                              /
/                                                                              /
================================================================================
*/


// 1. CHOOSE YOUR STM32 MCU FAMILY
//    Uncomment the line that matches your MCU.
//    (This is for STM32F1 series, which includes STM32F103C8T6)
//------------------------------------------------------------------------------
#define STM32F1


// 2. CHOOSE YOUR COMMUNICATION BUS
//    Uncomment only one of the two lines below.
//------------------------------------------------------------------------------
#define SSD1306_USE_I2C     // <-- ENABLE THIS FOR I2C COMMUNICATION
// #define SSD1306_USE_SPI


// 3. DEFINE SCREEN SIZE
//    These are the standard dimensions for a 0.96" OLED screen.
//    The library uses these values by default, so you don't need to uncomment
//    them unless you have a non-standard screen.
//------------------------------------------------------------------------------
// #define SSD1306_WIDTH           128
// #define SSD1306_HEIGHT          64


//==============================================================================
// 4. I2C CONFIGURATION
//    These settings are only used if `SSD1306_USE_I2C` is enabled above.
//------------------------------------------------------------------------------
//    Define the I2C handle that you configured in STM32CubeMX.
//    It's usually `hi2c1` for I2C1, `hi2c2` for I2C2, etc.
#define SSD1306_I2C_PORT        hi2c1

//    Define the I2C address of your OLED screen.
//    - For 0.96" screens, the 7-bit address is almost always 0x3C.
//    - The STM32 HAL library requires the address to be shifted left by one bit.
//    - If 0x3C doesn't work, try 0x3D.
#define SSD1306_I2C_ADDR        (0x3C << 1)


//==============================================================================
// 5. SPI CONFIGURATION (IGNORE THIS SECTION FOR I2C)
//    These settings are only used if `SSD1306_USE_SPI` is enabled.
//------------------------------------------------------------------------------
// #define SSD1306_SPI_PORT        hspi1
// #define SSD1306_CS_Port         OLED_CS_GPIO_Port
// #define SSD1306_CS_Pin          OLED_CS_Pin
// #define SSD1306_DC_Port         OLED_DC_GPIO_Port
// #define SSD1306_DC_Pin          OLED_DC_Pin
// #define SSD1306_Reset_Port      OLED_Res_GPIO_Port
// #define SSD1306_Reset_Pin       OLED_Res_Pin


//==============================================================================
// 6. FONT CONFIGURATION
//    To save memory, you can comment out the fonts you don't need.
//------------------------------------------------------------------------------
#define SSD1306_INCLUDE_FONT_6x8
#define SSD1306_INCLUDE_FONT_7x10
#define SSD1306_INCLUDE_FONT_11x18
#define SSD1306_INCLUDE_FONT_16x26
#define SSD1306_INCLUDE_FONT_16x24
#define SSD1306_INCLUDE_FONT_16x15

#endif /* __SSD1306_CONF_H__ */

