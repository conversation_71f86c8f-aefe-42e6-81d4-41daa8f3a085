#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"


#include "i2c.h"
#include "main.h"
#include "math.h"
#include "ssd1306_conf_template.h"
#include "usart.h"
#include "dma.h"
#include "ringbuffer.h"


#include "oled_app.h"
#include "scheduler.h"
#include "uart_app.h"

extern I2C_HandleTypeDef hi2c1;     // ������ i2c.c
extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_dma_buffer[128];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];



